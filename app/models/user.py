from pydantic import BaseModel, Field, field_validator
from typing import Any, Dict, Literal, Optional
from pymongo.database import Database
from minio import Minio

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "agent"]

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

class UserTenantDB(BaseModel):
    """
    User document from the tenant database -> users collection with tenant context
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "agent"]
    tenant_id: str
    tenant_name: str
    db: Database
    adb: Database
    minio: Optional[Minio] = None

    class Config:
        arbitrary_types_allowed = True