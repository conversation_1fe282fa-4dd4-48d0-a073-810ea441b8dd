# app/models/auth.py

from fastapi import Form, Request, Depends
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, Field
from typing import Optional, Union
from urllib.parse import urlparse
from app.helper.logger import setup_new_logging
from fastapi.exceptions import HTTPException
from app.core.database import get_tenant_id_and_name_from_slug

loggers = setup_new_logging(__name__)

class OAuth2PasswordRequestFormWithClientID(OAuth2PasswordRequestForm):
    """
    Extended OAuth2 form that includes client_id for tenant identification.
    """
    def __init__(
        self,
        grant_type: str = Form(None, regex="password"),
        username: str = Form(...),
        password: str = Form(...),
        scope: str = Form(""),
        client_id: str = Form(...),  # Add client_id as a required field
    ):
        super().__init__(grant_type=grant_type, username=username, password=password, scope=scope)
        self.client_id = client_id

async def get_login_form_with_referrer_check(
    request: Request,
    form_data: OAuth2PasswordRequestFormWithClientID = Depends(),
) -> OAuth2PasswordRequestFormWithClientID:
    """
    Check referrer and automatically set client_id based on subdomain if needed.
    """
    referrer_url = request.headers.get("referer") or request.headers.get("referrer") or request.headers.get("origin")
    hostname = None

    if referrer_url:
        parsed_referrer = urlparse(referrer_url)
        hostname = parsed_referrer.hostname

    # Fallback if no referrer or origin is found
    if not hostname:
        return form_data

    loggers.debug(f"Original client_id: {form_data.client_id}, Hostname: {hostname}")

    # If client_id is provided and not default, respect it
    if form_data.client_id != "legal":
        return form_data

    # Extract domain and subdomain
    domain_parts = hostname.split(".")
    if len(domain_parts) >= 3:
        # e.g. abc.xyz.com → subdomain = abc, domain = xyz.com
        subdomain = domain_parts[0]
        domain = ".".join(domain_parts[1:])
    elif len(domain_parts) == 2:
        subdomain = None
        domain = ".".join(domain_parts)
    else:
        return form_data  # Invalid domain

    if subdomain:
        loggers.debug(f"Identified subdomain as a slug: {subdomain}")
        tenant_info = get_tenant_id_and_name_from_slug(subdomain)
        if tenant_info:
            form_data.client_id = subdomain

    return form_data

class ChangePasswordRequest(BaseModel):
    """
    Request model for changing password.
    """
    old_password: str
    new_password: str

class ResetPasswordRequest(BaseModel):
    """
    Request model for resetting password.
    """
    subordinate_id: Optional[str] = None

class ExtendedTokenRequest(BaseModel):
    """
    Request model for extended token validity.
    """
    username: str
    password: str
    client_id: str
    days: Union[int, float] = Field(..., description="Number of days for token validity", ge=1, le=365)

class UserRegistration(BaseModel):
    """
    Request model for user registration.
    """
    username: str
    password: str
    role: str = "agent"
    db_name: str
    isBot: bool = False

class AgentInvitation(BaseModel):
    """
    Request model for agent invitation.
    """
    username: str
    role: str = "agent"
    expires_at: int = 7  # Days
    permissions: Optional[dict] = None

class AgentRegistration(BaseModel):
    """
    Request model for agent registration via invitation.
    """
    username: str
    password: str
    token: str

class LoginResponse(BaseModel):
    """
    Response model for successful login.
    """
    id: str
    access_token: str
    token_type: str = "bearer"
    username: str
    role: str
    tenant_id: str
    tenant_label: str
    tenant_slug: str
    nav_permission: Optional[dict] = None
    token_validity: dict
    expires_at: str
