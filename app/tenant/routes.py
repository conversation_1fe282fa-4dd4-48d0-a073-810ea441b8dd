# app/tenant/routes.py

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from app.tenant.management import tenant_manager
from app.models.auth import TenantCreation
from app.models.user import TenantInfo, SuperAdmin
from app.core.security import get_admin_db
from app.helper.logger import setup_new_logging

router = APIRouter(prefix="/admin/tenants", tags=["Tenant Management"])
logger = setup_new_logging(__name__)

def get_super_admin() -> SuperAdmin:
    """
    Dependency to verify super admin access.
    For now, this is a placeholder - in production you'd verify JWT tokens.
    """
    # TODO: Implement proper super admin authentication
    # This is a simplified version for demonstration
    admin_db = get_admin_db()
    super_admin = admin_db.users.find_one({"role": "superadmin", "is_active": True})
    if not super_admin:
        raise HTTPException(status_code=403, detail="Super admin access required")
    return SuperAdmin(**super_admin)

@router.post("/create", response_model=dict)
async def create_tenant(
    tenant_data: TenantCreation,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Create a new tenant. Only super admins can create tenants.
    """
    try:
        tenant_id = await tenant_manager.create_tenant(tenant_data)
        return {
            "success": True,
            "tenant_id": tenant_id,
            "message": f"Tenant '{tenant_data.name}' created successfully"
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error creating tenant: {e}")
        raise HTTPException(status_code=500, detail="Failed to create tenant")

@router.get("/list", response_model=List[TenantInfo])
async def list_tenants(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    List all tenants with pagination.
    """
    return tenant_manager.list_tenants(skip=skip, limit=limit)

@router.get("/{tenant_id}", response_model=TenantInfo)
async def get_tenant(
    tenant_id: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Get tenant information by ID.
    """
    tenant = tenant_manager.get_tenant(tenant_id)
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return tenant

@router.get("/slug/{slug}", response_model=TenantInfo)
async def get_tenant_by_slug(
    slug: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Get tenant information by slug.
    """
    tenant = tenant_manager.get_tenant_by_slug(slug)
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return tenant

@router.put("/{tenant_id}/activate")
async def activate_tenant(
    tenant_id: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Activate a tenant.
    """
    success = tenant_manager.activate_tenant(tenant_id)
    if not success:
        raise HTTPException(status_code=404, detail="Tenant not found or update failed")
    return {"success": True, "message": "Tenant activated successfully"}

@router.put("/{tenant_id}/deactivate")
async def deactivate_tenant(
    tenant_id: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Deactivate a tenant.
    """
    success = tenant_manager.deactivate_tenant(tenant_id)
    if not success:
        raise HTTPException(status_code=404, detail="Tenant not found or update failed")
    return {"success": True, "message": "Tenant deactivated successfully"}

@router.get("/{tenant_id}/stats")
async def get_tenant_stats(
    tenant_id: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Get tenant statistics.
    """
    stats = tenant_manager.get_tenant_stats(tenant_id)
    if not stats:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return stats

# Public endpoint for tenant info (used by login)
@router.get("/public/slug/{slug}")
async def get_public_tenant_info(slug: str):
    """
    Get basic tenant information by slug (public endpoint for login).
    """
    tenant = tenant_manager.get_tenant_by_slug(slug)
    if not tenant or not tenant.is_active:
        raise HTTPException(status_code=404, detail="Tenant not found")
    
    return {
        "tenant_id": tenant.id,
        "name": tenant.name,
        "label": tenant.label,
        "slug": tenant.slug
    }
