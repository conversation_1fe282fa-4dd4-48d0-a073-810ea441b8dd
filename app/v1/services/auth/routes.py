# app/v1/services/auth/routes.py

from fastapi import APIRouter, HTTPException, Depends, status
from app.shared.database.connection import get_tenant_id_and_name_from_slug
from app.shared.database.models import UserTenantDB
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging
from .models import (
    OAuth2PasswordRequestFormWithClientID,
    get_login_form_with_referrer_check,
    ExtendedTokenRequest,
    LoginResponse
)
from .service import AuthService

router = APIRouter(prefix="/auth", tags=["Authentication"])
logger = setup_new_logging(__name__)

@router.get("/tenant/{slug}")
async def get_tenant_info(slug: str):
    """
    Get tenant ID and name from slug.
    """
    try:
        result = get_tenant_id_and_name_from_slug(slug)
        if not result:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Tenant not found")

        # Check if tenant is active
        if not result.get("is_active", True):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant is inactive")

        tenant_id = str(result["_id"])
        tenant_name = result["name"]

        return {
            "tenant_id": tenant_id,
            "tenant_name": tenant_name,
            "tenant_label": result.get("label", tenant_name),
            "tenant_slug": result["slug"]
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error getting tenant ID: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/login", response_model=LoginResponse)
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends(get_login_form_with_referrer_check)):
    """
    Login endpoint with Argon2 password verification.
    """
    logger.info(f"Processing login with client_id: {form_data.client_id}")
    
    try:
        return await AuthService.authenticate_user(
            username=form_data.username,
            password=form_data.password,
            client_id=form_data.client_id
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/verify")
async def verify_token(current_user: UserTenantDB = Depends(get_current_user)):
    """
    Verify if the token is valid and return user details.
    """
    return {
        "valid": True,
        "username": current_user.username,
        "role": current_user.role,
        "tenant_id": current_user.tenant_id,
        "tenant_name": current_user.tenant_name,
        "tenant_slug": current_user.tenant_slug
    }

@router.post("/extended-token")
async def get_extended_token(request: ExtendedTokenRequest):
    """
    Generate an access token with extended validity period.
    """
    try:
        return await AuthService.get_extended_token(request)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Extended token error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
