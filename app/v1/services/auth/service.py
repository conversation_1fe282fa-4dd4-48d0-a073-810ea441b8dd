# app/v1/services/auth/service.py

from datetime import datetime, timedelta, timezone
from typing import Optional
from pymongo.errors import PyMongoError
from fastapi import HTTPException, status

from app.shared.database.connection import get_db_from_tenant_id, get_tenant_id_and_name_from_slug
from app.shared.security.auth import (
    hash_password, 
    verify_password, 
    create_access_token,
    create_invitation_token,
    verify_invitation_token
)
from app.shared.utils.helpers import convert_objectid_to_str
from app.shared.utils.logger import setup_new_logging
from .models import (
    UserRegistration,
    UserInvitation,
    UserRegistrationViaInvitation,
    LoginResponse,
    ExtendedTokenRequest
)

logger = setup_new_logging(__name__)

class AuthService:
    """
    Authentication service for handling user authentication and management.
    """
    
    @staticmethod
    async def authenticate_user(username: str, password: str, client_id: str) -> LoginResponse:
        """
        Authenticate a user and return login response.
        """
        # Find the tenant database
        result = get_tenant_id_and_name_from_slug(client_id)
        if not result:
            raise HTTPException(status_code=404, detail="Tenant not found")
        
        # Check if tenant is active
        if not result.get("is_active", True):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant is inactive")

        tenant_id = str(result["_id"])
        tenant_database = get_db_from_tenant_id(tenant_id)
        
        # Connect to the user_collection of that database
        user = tenant_database.users.find_one({"username": username})
        
        if not user:
            raise HTTPException(status_code=401, detail="User not found")

        # Verify password using Argon2
        if not verify_password(password, user["hashed_password"]):
            raise HTTPException(status_code=401, detail="Incorrect Credentials")

        # Check if user is active
        if not user.get("is_active", True):
            raise HTTPException(status_code=403, detail="User account is disabled")

        # Get navigation permissions
        try:
            permissions_setting = tenant_database.settings.find_one({"name": "permissions"})
            nav_permission = permissions_setting.get(user["role"]) if permissions_setting else None
        except Exception as e:
            logger.warning(f"Could not get nav permissions: {e}")
            nav_permission = None

        # Get token validity settings
        days = 0
        hours = 0
        minutes = 0
        seconds = 0
        
        try:
            token_settings = tenant_database.settings.find_one({"name": "token_validity"})
            if token_settings:
                days = token_settings.get("days", 0)
                hours = token_settings.get("hours", 0)
                minutes = token_settings.get("minutes", 0)
                seconds = token_settings.get("seconds", 0)

            expires_delta = timedelta(
                days=days,
                hours=hours,
                minutes=minutes,
                seconds=seconds
            )
            
            logger.info(f"Token validity settings: {token_settings}")

            # If no time components were found, use default
            if days == 0 and hours == 0 and minutes == 0 and seconds == 0:
                logger.warning("No token validity settings found. Using default (8 hours).")
                expires_delta = timedelta(hours=8)  # Default: 8 hours

        except Exception as e:
            # Fallback to 8 hours if any error occurs
            expires_delta = timedelta(hours=8)
            logger.warning(f"Error fetching token validity settings: {str(e)}. Using default (8 hours).")

        # Create access token
        access_token = create_access_token(
            data={"sub": user["username"], "user_id": str(user["_id"]), "role": user["role"], "tenant_id": tenant_id},
            expires_delta=expires_delta
        )

        # Calculate expiration date for response
        expiration_date = datetime.now(timezone.utc) + expires_delta

        user = convert_objectid_to_str(user)

        return LoginResponse(
            id=user["_id"],
            access_token=access_token,
            token_type="bearer",
            username=user['username'],
            role=user['role'],
            tenant_id=tenant_id,
            tenant_label=result.get("label", result["name"]),
            tenant_slug=client_id,
            nav_permission=nav_permission,
            token_validity={
                "days": days,
                "hours": hours,
                "minutes": minutes,
                "seconds": seconds,
                "total_seconds": expires_delta.total_seconds()
            },
            expires_at=expiration_date.isoformat()
        )

    @staticmethod
    async def get_extended_token(request: ExtendedTokenRequest) -> dict:
        """
        Generate an access token with extended validity period.
        """
        # Find the tenant database
        result = get_tenant_id_and_name_from_slug(request.client_id)
        if not result:
            raise HTTPException(status_code=404, detail="Tenant not found")

        tenant_id = str(result["_id"])
        tenant_database = get_db_from_tenant_id(tenant_id)

        # Authenticate the user
        user = tenant_database.users.find_one({"username": request.username})
        if not user:
            raise HTTPException(status_code=401, detail="User not found")

        if not verify_password(request.password, user["hashed_password"]):
            raise HTTPException(status_code=401, detail="Incorrect credentials")

        # Generate token with extended validity
        access_token = create_access_token(
            data={"sub": user["username"], "user_id": str(user["_id"]), "role": user["role"], "tenant_id": tenant_id},
            expires_delta=timedelta(days=request.days)
        )

        # Get navigation permissions if available
        try:
            permissions_setting = tenant_database.settings.find_one({"name": "permissions"})
            nav_permission = permissions_setting.get(user["role"]) if permissions_setting else None
        except Exception:
            nav_permission = None

        # Convert ObjectId to string for JSON serialization
        user = convert_objectid_to_str(user)

        # Log the extended token generation
        logger.info(f"Extended token generated for user {request.username} with validity of {request.days} days")

        return {
            "id": user["_id"],
            "access_token": access_token,
            "token_type": "bearer",
            "username": user['username'],
            "role": user['role'],
            "tenant_id": tenant_id,
            "tenant_label": result.get("label", result["name"]),
            "tenant_slug": request.client_id,
            "nav_permission": nav_permission,
            "token_validity": {
                "days": request.days,
                "hours": 0,
                "minutes": 0,
                "seconds": 0,
                "total_seconds": timedelta(days=request.days).total_seconds()
            },
            "expires_at": (datetime.now(timezone.utc) + timedelta(days=request.days)).isoformat()
        }
