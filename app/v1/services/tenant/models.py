# app/v1/services/tenant/models.py

from pydantic import BaseModel
from typing import Optional, Dict

class TenantCreation(BaseModel):
    """
    Request model for creating a new tenant.
    """
    name: str
    slug: str
    label: Optional[str] = None
    admin_username: str = "admin"
    admin_password: str
    settings: Optional[dict] = None

class TenantUpdate(BaseModel):
    """
    Request model for updating tenant information.
    """
    name: Optional[str] = None
    label: Optional[str] = None
    is_active: Optional[bool] = None
    settings: Optional[dict] = None

class TenantStats(BaseModel):
    """
    Response model for tenant statistics.
    """
    users_count: int
    active_users_count: int
    documents_count: int
    recent_activity: int
    tenant_name: str
    tenant_slug: str
