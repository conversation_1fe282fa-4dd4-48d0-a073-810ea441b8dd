# app/v1/services/tenant/service.py

from datetime import datetime
from typing import Optional, Dict, List
from bson import ObjectId
from pymongo.errors import Duplicate<PERSON>eyError
from fastapi import HTTPException

from app.shared.database.connection import (
    get_admin_db, 
    get_db_from_tenant_id, 
    init_tenant_database,
    create_default_tenant_admin
)
from app.shared.database.models import TenantInfo
from app.shared.utils.logger import setup_new_logging
from .models import TenantCreation, TenantUpdate, TenantStats

logger = setup_new_logging(__name__)

class TenantService:
    """
    Service for managing tenant operations.
    """
    
    def __init__(self):
        self.admin_db = get_admin_db()
    
    async def create_tenant(self, tenant_data: TenantCreation) -> str:
        """
        Create a new tenant with its database and default admin user.
        """
        try:
            # Prepare tenant document
            tenant_doc = {
                "name": tenant_data.name,
                "slug": tenant_data.slug.lower().replace(" ", "-"),
                "label": tenant_data.label or tenant_data.name,
                "database_name": f"legal_{tenant_data.slug.lower().replace(' ', '_').replace('-', '_')}_db",
                "created_at": datetime.now(),
                "is_active": True,
                "settings": tenant_data.settings or {
                    "max_users": 50,
                    "max_storage_gb": 10,
                    "features": ["document_processing", "user_management"]
                }
            }
            
            # Insert tenant into admin database
            result = self.admin_db.tenants.insert_one(tenant_doc)
            tenant_id = str(result.inserted_id)
            
            logger.info(f"Created tenant '{tenant_data.name}' with ID: {tenant_id}")
            
            # Initialize tenant database
            init_tenant_database(tenant_id)
            
            # Create default admin user
            admin_user_id = create_default_tenant_admin(
                tenant_id, 
                tenant_data.admin_username, 
                tenant_data.admin_password
            )
            
            logger.info(f"Created admin user for tenant {tenant_id}")
            
            return tenant_id
            
        except DuplicateKeyError:
            raise HTTPException(status_code=400, detail="Tenant slug already exists")
        except Exception as e:
            logger.error(f"Error creating tenant: {e}")
            raise HTTPException(status_code=500, detail="Failed to create tenant")
    
    def get_tenant(self, tenant_id: str) -> Optional[TenantInfo]:
        """
        Get tenant information by ID.
        """
        try:
            tenant = self.admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
            if tenant:
                return TenantInfo(**tenant)
            return None
        except Exception as e:
            logger.error(f"Error getting tenant {tenant_id}: {e}")
            return None
    
    def get_tenant_by_slug(self, slug: str) -> Optional[TenantInfo]:
        """
        Get tenant information by slug.
        """
        try:
            tenant = self.admin_db.tenants.find_one({"slug": slug})
            if tenant:
                return TenantInfo(**tenant)
            return None
        except Exception as e:
            logger.error(f"Error getting tenant by slug {slug}: {e}")
            return None
    
    def list_tenants(self, skip: int = 0, limit: int = 50) -> List[TenantInfo]:
        """
        List all tenants with pagination.
        """
        try:
            tenants = self.admin_db.tenants.find().skip(skip).limit(limit)
            return [TenantInfo(**tenant) for tenant in tenants]
        except Exception as e:
            logger.error(f"Error listing tenants: {e}")
            return []
    
    def update_tenant(self, tenant_id: str, updates: TenantUpdate) -> bool:
        """
        Update tenant information.
        """
        try:
            update_data = updates.dict(exclude_unset=True)
            if not update_data:
                return False
                
            update_data["updated_at"] = datetime.now()
            result = self.admin_db.tenants.update_one(
                {"_id": ObjectId(tenant_id)},
                {"$set": update_data}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating tenant {tenant_id}: {e}")
            return False
    
    def deactivate_tenant(self, tenant_id: str) -> bool:
        """
        Deactivate a tenant (soft delete).
        """
        return self.update_tenant(tenant_id, TenantUpdate(is_active=False))
    
    def activate_tenant(self, tenant_id: str) -> bool:
        """
        Activate a tenant.
        """
        return self.update_tenant(tenant_id, TenantUpdate(is_active=True))
    
    def get_tenant_stats(self, tenant_id: str) -> TenantStats:
        """
        Get statistics for a tenant.
        """
        try:
            tenant_info = self.get_tenant(tenant_id)
            if not tenant_info:
                raise HTTPException(status_code=404, detail="Tenant not found")
                
            tenant_db = get_db_from_tenant_id(tenant_id)
            
            stats_data = {
                "users_count": tenant_db.users.count_documents({}),
                "active_users_count": tenant_db.users.count_documents({"is_active": True}),
                "documents_count": tenant_db.documents.count_documents({}),
                "recent_activity": tenant_db.activity_logs.count_documents({
                    "timestamp": {"$gte": datetime.now().replace(hour=0, minute=0, second=0)}
                }),
                "tenant_name": tenant_info.name,
                "tenant_slug": tenant_info.slug
            }
            
            return TenantStats(**stats_data)
        except Exception as e:
            logger.error(f"Error getting tenant stats for {tenant_id}: {e}")
            raise HTTPException(status_code=500, detail="Error retrieving tenant statistics")

# Global tenant service instance
tenant_service = TenantService()
