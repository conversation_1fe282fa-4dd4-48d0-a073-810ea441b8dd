# app/v1/services/tenant/routes.py

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List

from app.shared.database.models import TenantInfo, SuperAdmin
from app.shared.security.dependencies import get_super_admin
from app.shared.utils.logger import setup_new_logging
from .models import TenantCreation, TenantUpdate, TenantStats
from .service import tenant_service

router = APIRouter(prefix="/admin/tenants", tags=["Tenant Management"])
logger = setup_new_logging(__name__)

@router.post("/create", response_model=dict)
async def create_tenant(
    tenant_data: TenantCreation,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Create a new tenant. Only super admins can create tenants.
    """
    try:
        tenant_id = await tenant_service.create_tenant(tenant_data)
        return {
            "success": True,
            "tenant_id": tenant_id,
            "message": f"Tenant '{tenant_data.name}' created successfully"
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error creating tenant: {e}")
        raise HTTPException(status_code=500, detail="Failed to create tenant")

@router.get("/list", response_model=List[TenantInfo])
async def list_tenants(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    List all tenants with pagination.
    """
    return tenant_service.list_tenants(skip=skip, limit=limit)

@router.get("/{tenant_id}", response_model=TenantInfo)
async def get_tenant(
    tenant_id: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Get tenant information by ID.
    """
    tenant = tenant_service.get_tenant(tenant_id)
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return tenant

@router.get("/slug/{slug}", response_model=TenantInfo)
async def get_tenant_by_slug(
    slug: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Get tenant information by slug.
    """
    tenant = tenant_service.get_tenant_by_slug(slug)
    if not tenant:
        raise HTTPException(status_code=404, detail="Tenant not found")
    return tenant

@router.put("/{tenant_id}")
async def update_tenant(
    tenant_id: str,
    updates: TenantUpdate,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Update tenant information.
    """
    success = tenant_service.update_tenant(tenant_id, updates)
    if not success:
        raise HTTPException(status_code=404, detail="Tenant not found or no changes made")
    return {"success": True, "message": "Tenant updated successfully"}

@router.put("/{tenant_id}/activate")
async def activate_tenant(
    tenant_id: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Activate a tenant.
    """
    success = tenant_service.activate_tenant(tenant_id)
    if not success:
        raise HTTPException(status_code=404, detail="Tenant not found or update failed")
    return {"success": True, "message": "Tenant activated successfully"}

@router.put("/{tenant_id}/deactivate")
async def deactivate_tenant(
    tenant_id: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Deactivate a tenant.
    """
    success = tenant_service.deactivate_tenant(tenant_id)
    if not success:
        raise HTTPException(status_code=404, detail="Tenant not found or update failed")
    return {"success": True, "message": "Tenant deactivated successfully"}

@router.get("/{tenant_id}/stats", response_model=TenantStats)
async def get_tenant_stats(
    tenant_id: str,
    super_admin: SuperAdmin = Depends(get_super_admin)
):
    """
    Get tenant statistics.
    """
    return tenant_service.get_tenant_stats(tenant_id)

# Public endpoint for tenant info (used by login)
@router.get("/public/slug/{slug}")
async def get_public_tenant_info(slug: str):
    """
    Get basic tenant information by slug (public endpoint for login).
    """
    tenant = tenant_service.get_tenant_by_slug(slug)
    if not tenant or not tenant.is_active:
        raise HTTPException(status_code=404, detail="Tenant not found")
    
    return {
        "tenant_id": tenant.id,
        "name": tenant.name,
        "label": tenant.label,
        "slug": tenant.slug
    }
