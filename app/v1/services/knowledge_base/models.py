# app/v1/services/knowledge_base/models.py

from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime

class DocumentUploadResponse(BaseModel):
    """
    Response model for document upload.
    """
    message: str
    nodes_created: int
    files_processed: List[str]
    processed_by: str
    tenant: str
    upload_id: Optional[str] = None

class DocumentMetadata(BaseModel):
    """
    Document metadata model.
    """
    filename: str
    file_size: int
    content_type: str
    uploaded_at: datetime
    uploaded_by: str
    tenant_id: str
    processing_status: str = "pending"
    
class ProcessingResult(BaseModel):
    """
    Document processing result model.
    """
    document_id: str
    status: str
    pages_processed: int
    text_extracted: bool
    images_extracted: int
    errors: Optional[List[str]] = None
