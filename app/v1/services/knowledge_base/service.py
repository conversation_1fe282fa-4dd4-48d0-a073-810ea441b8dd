# app/v1/services/knowledge_base/service.py

import fitz  # PyMuPDF
from typing import List, Dict
from fastapi import UploadFile, HTTPException
from datetime import datetime
from llama_index.core import Document
import hashlib
import re
import uuid

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from .splitters import SentenceSplitter as CustomSentenceSplitter

logger = setup_new_logging(__name__)

class KnowledgeBaseService:
    """
    Simple service for handling PDF processing and knowledge base operations.
    """

    @staticmethod
    async def process_pdf_documents(files: List[UploadFile], current_user: UserTenantDB) -> dict:
        """
        Process Nepali PDF documents and create two collections:
        1. Whole documents (without embeddings) - stored in MongoDB
        2. Split documents (with embeddings) - stored in Qdrant
        """
        try:
            logger.info(f"User {current_user.username} is processing {len(files)} Nepali PDF documents")

            # Initialize Qdrant only when needed to save resources
            await current_user.init_qdrant()

            whole_documents = []
            split_documents = []

            for file in files:
                # Read PDF content
                pdf_content = await file.read()
                doc = fitz.open(stream=pdf_content, filetype="pdf")

                # Extract full text from PDF with proper Nepali text handling
                full_text = ""
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    # Get text with proper encoding for Nepali
                    page_text = page.get_text("text", flags=fitz.TEXT_PRESERVE_WHITESPACE)
                    full_text += page_text + "\n"

                # Clean and normalize Nepali text
                full_text = KnowledgeBaseService._clean_nepali_text(full_text)

                # Create document ID
                doc_id = hashlib.sha256(f"{file.filename}_{current_user.tenant_id}".encode()).hexdigest()

                # Create whole document (without embeddings)
                whole_doc = Document(
                    text=full_text,
                    metadata={
                        "filename": file.filename,
                        "doc_id": doc_id,
                        "tenant_id": current_user.tenant_id,
                        "uploaded_by": current_user.username,
                        "uploaded_at": datetime.now().isoformat(),
                        "type": "whole_document",
                        "language": "nepali"
                    }
                )
                whole_documents.append(whole_doc)

                # Create split documents using custom splitter (like echo_bot)
                splitter = CustomSentenceSplitter(
                    max_chunk_length=400,  # Smaller chunks for Nepali
                    overlap_sentences=1,
                    min_sentences_per_chunk=3
                )

                # Split the document using custom splitter
                split_nodes = splitter([whole_doc])

                # Add metadata to split nodes
                for i, node in enumerate(split_nodes):
                    node.metadata.update({
                        "chunk_id": f"{doc_id}_chunk_{i}",
                        "parent_doc_id": doc_id,
                        "chunk_index": i,
                        "type": "split_document",
                        "language": "nepali"
                    })

                split_documents.extend(split_nodes)
                doc.close()

            # Store in collections using async database and Qdrant
            await KnowledgeBaseService._store_in_collections(
                whole_documents,
                split_documents,
                current_user
            )

            # Store metadata in async database
            await KnowledgeBaseService._store_document_metadata(files, current_user)

            logger.info(f"Successfully processed {len(files)} Nepali PDF files for user {current_user.username}")

            return {
                "message": f"Successfully processed {len(files)} Nepali PDF files",
                "whole_documents_created": len(whole_documents),
                "split_documents_created": len(split_documents),
                "files_processed": [file.filename for file in files],
                "processed_by": current_user.username,
                "tenant": current_user.tenant_name
            }

        except Exception as e:
            logger.error(f"Error processing Nepali PDF documents for user {current_user.username}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def process_documents_to_llamaindex(files: List[UploadFile], current_user: UserTenantDB) -> dict:
        """
        Process PDF documents and add to LlamaIndex collections.
        Collection names are retrieved from MongoDB settings.
        """
        try:
            logger.info(f"User {current_user.username} is processing {len(files)} PDF documents for LlamaIndex")

            # Get collection names from MongoDB settings
            collection_settings = await KnowledgeBaseService._get_collection_settings(current_user)

            # Initialize Qdrant only when needed
            await current_user.init_qdrant()

            whole_documents = []
            split_documents = []

            for file in files:
                # Read PDF content
                pdf_content = await file.read()
                doc = fitz.open(stream=pdf_content, filetype="pdf")

                # Extract full text from PDF with proper Nepali text handling
                full_text = ""
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    # Get text with proper encoding for Nepali
                    page_text = page.get_text("text", flags=fitz.TEXT_PRESERVE_WHITESPACE)
                    full_text += page_text + "\n"

                # Clean and normalize Nepali text
                full_text = KnowledgeBaseService._clean_nepali_text(full_text)

                # Create document ID
                doc_id = hashlib.sha256(f"{file.filename}_{current_user.tenant_id}".encode()).hexdigest()

                # Create whole document (without embeddings)
                whole_doc = Document(
                    text=full_text,
                    metadata={
                        "filename": file.filename,
                        "doc_id": doc_id,
                        "tenant_id": current_user.tenant_id,
                        "uploaded_by": current_user.username,
                        "uploaded_at": datetime.now().isoformat(),
                        "type": "whole_document",
                        "language": "nepali"
                    }
                )
                whole_documents.append(whole_doc)

                # Create split documents using custom splitter
                splitter = CustomSentenceSplitter(
                    max_chunk_length=400,  # Smaller chunks for Nepali
                    overlap_sentences=1,
                    min_sentences_per_chunk=3
                )

                # Split the document using custom splitter
                split_nodes = splitter([whole_doc])

                # Add metadata to split nodes
                for i, node in enumerate(split_nodes):
                    node.metadata.update({
                        "chunk_id": f"{doc_id}_chunk_{i}",
                        "parent_doc_id": doc_id,
                        "chunk_index": i,
                        "type": "split_document",
                        "language": "nepali"
                    })

                split_documents.extend(split_nodes)
                doc.close()

            # Store in collections (simplified for now)
            await KnowledgeBaseService._store_in_collections(
                whole_documents,
                split_documents,
                current_user
            )

            # Store metadata in database
            await KnowledgeBaseService._store_document_metadata(files, current_user)

            logger.info(f"Successfully processed {len(files)} PDF files to LlamaIndex for user {current_user.username}")

            # Get MinIO config for response
            minio_config = await current_user.get_minio_config()

            return {
                "message": f"Successfully processed {len(files)} PDF files to LlamaIndex",
                "whole_documents_added": len(whole_documents),
                "split_documents_added": len(split_documents),
                "files_processed": [file.filename for file in files],
                "processed_by": current_user.username,
                "tenant": current_user.tenant_name,
                "collections": {
                    "whole_docs_collection": collection_settings["whole_docs_collection"],
                    "split_docs_collection": collection_settings["split_docs_collection"],
                    "page_collection": collection_settings["page_collection"],
                    "legal_sentence": collection_settings["legal_sentence"]
                },
                "storage": {
                    "qdrant_host": "*************:6333",
                    "minio_bucket": minio_config["bucket_name"],
                    "minio_url": minio_config["minio_url"]
                }
            }

        except Exception as e:
            logger.error(f"Error processing PDF documents to LlamaIndex for user {current_user.username}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def get_collection_settings(current_user: UserTenantDB) -> dict:
        """
        Public method to get collection settings from environment/MongoDB.
        """
        try:
            collection_settings = await KnowledgeBaseService._get_collection_settings(current_user)

            # Also get Qdrant and MinIO connection info
            qdrant_config = await current_user.adb.settings.find_one({"name": "qdrant_config"})
            minio_config = await current_user.get_minio_config()

            return {
                "tenant": current_user.tenant_name,
                "tenant_slug": current_user.tenant_slug,
                "collections": collection_settings,
                "qdrant_config": {
                    "host": qdrant_config.get("host", "*************"),
                    "port": qdrant_config.get("port", 6333),
                    "timeout": qdrant_config.get("timeout", 60)
                } if qdrant_config else None,
                "minio_config": {
                    "bucket_name": minio_config["bucket_name"],
                    "minio_url": minio_config["minio_url"],
                    "secure": minio_config["secure"]
                    # Note: access_key and secret_key are not exposed for security
                }
            }

        except Exception as e:
            logger.error(f"Error getting collection settings: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def _get_collection_settings(current_user: UserTenantDB) -> dict:
        """
        Get collection names from Qdrant config in MongoDB settings.
        """
        try:
            # Get Qdrant config which includes collection names
            qdrant_config = await current_user.adb.settings.find_one({"name": "qdrant_config"})

            if not qdrant_config:
                # Initialize Qdrant to create default config with collection names
                await current_user.init_qdrant()
                qdrant_config = await current_user.adb.settings.find_one({"name": "qdrant_config"})

            # Extract collection names from Qdrant config
            return {
                "whole_docs_collection": qdrant_config.get("whole_docs_collection", f"{current_user.tenant_slug}_whole_documents"),
                "split_docs_collection": qdrant_config.get("split_docs_collection", f"{current_user.tenant_slug}_split_documents"),
                "page_collection": qdrant_config.get("page_collection", f"{current_user.tenant_slug}_page"),
                "legal_sentence": qdrant_config.get("legal_sentence", f"{current_user.tenant_slug}_sentence")
            }

        except Exception as e:
            logger.error(f"Error getting collection settings from Qdrant config: {e}")
            # Fallback to default names
            return {
                "whole_docs_collection": f"{current_user.tenant_slug}_whole_documents",
                "split_docs_collection": f"{current_user.tenant_slug}_split_documents",
                "page_collection": f"{current_user.tenant_slug}_page",
                "legal_sentence": f"{current_user.tenant_slug}_sentence"
            }



    @staticmethod
    def _clean_nepali_text(text: str) -> str:
        """
        Clean and normalize Nepali text extracted from PDF.
        """
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove empty lines
        text = re.sub(r'\n\s*\n', '\n', text)

        # Normalize Nepali characters (if needed)
        # Add specific Nepali text normalization here

        # Remove leading/trailing whitespace
        text = text.strip()

        return text

    @staticmethod
    async def _store_in_collections(whole_documents: List[Document], split_documents: List[Document], current_user: UserTenantDB):
        """
        Store documents in two separate collections:
        1. Whole documents (without embeddings) - MongoDB
        2. Split documents (with embeddings) - Qdrant
        """
        try:
            # Collection names
            whole_docs_collection = f"{current_user.tenant_slug}_whole_documents"
            qdrant_collection = f"{current_user.tenant_slug}_split_documents"

            logger.info(f"Storing {len(whole_documents)} whole documents and {len(split_documents)} split documents")

            # Collection 1: Store whole documents in MongoDB (without embeddings)
            if whole_documents:
                documents_to_insert = []
                for doc in whole_documents:
                    doc_dict = {
                        "text": doc.text,
                        "metadata": doc.metadata,
                        "created_at": datetime.now()
                    }
                    documents_to_insert.append(doc_dict)

                # Insert into MongoDB using async client
                await current_user.adb[whole_docs_collection].insert_many(documents_to_insert)
                logger.info(f"Stored {len(whole_documents)} whole documents in MongoDB collection: {whole_docs_collection}")

            # Collection 2: Store split documents in Qdrant (with embeddings)
            if split_documents:
                await KnowledgeBaseService._store_in_qdrant(split_documents, qdrant_collection, current_user)
                logger.info(f"Stored {len(split_documents)} split documents in Qdrant collection: {qdrant_collection}")

        except Exception as e:
            logger.error(f"Error storing documents in collections: {e}")
            raise

    @staticmethod
    async def _store_in_qdrant(documents: List[Document], collection_name: str, current_user: UserTenantDB):
        """
        Store documents in Qdrant with embeddings.
        """
        try:
            from qdrant_client.models import Distance, VectorParams, PointStruct

            # Ensure Qdrant is initialized
            qdrant_client = await current_user.init_qdrant()

            # Check if collection exists, create if not
            collections = await qdrant_client.get_collections()
            collection_exists = any(col.name == collection_name for col in collections.collections)

            if not collection_exists:
                # Create collection with vector configuration
                await qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(
                        size=1536,  # OpenAI embedding size
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created Qdrant collection: {collection_name}")

            # Prepare points for insertion
            points = []
            for doc in documents:
                # Generate a simple embedding (in production, use proper embedding model)
                # For now, just create a dummy vector
                vector = [0.0] * 1536  # Dummy vector, replace with actual embeddings

                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=vector,
                    payload={
                        "text": doc.text,
                        "metadata": doc.metadata
                    }
                )
                points.append(point)

            # Insert points into Qdrant
            await qdrant_client.upsert(
                collection_name=collection_name,
                points=points
            )

            logger.info(f"Inserted {len(points)} points into Qdrant collection: {collection_name}")

        except Exception as e:
            logger.error(f"Error storing documents in Qdrant: {e}")
            raise
    
    @staticmethod
    async def _store_document_metadata(files: List[UploadFile], current_user: UserTenantDB):
        """
        Store document metadata in the async database.
        """
        try:
            documents_collection = current_user.adb.documents

            metadata_list = []
            for file in files:
                metadata = {
                    "filename": file.filename,
                    "file_size": file.size if hasattr(file, 'size') else 0,
                    "content_type": file.content_type,
                    "uploaded_at": datetime.now(),
                    "uploaded_by": current_user.username,
                    "tenant_id": current_user.tenant_id,
                    "processing_status": "completed",
                    "language": "nepali"
                }
                metadata_list.append(metadata)

            # Insert all metadata at once using async client
            await documents_collection.insert_many(metadata_list)
            logger.info(f"Stored metadata for {len(files)} documents")

        except Exception as e:
            logger.error(f"Error storing document metadata: {e}")
            # Don't raise exception here as document processing was successful
    
    @staticmethod
    async def get_documents(current_user: UserTenantDB, skip: int = 0, limit: int = 50) -> List[dict]:
        """
        Get list of documents for the current tenant using async database.
        """
        try:
            documents_collection = current_user.adb.documents
            cursor = documents_collection.find().skip(skip).limit(limit).sort("uploaded_at", -1)
            documents = await cursor.to_list(length=limit)

            # Convert ObjectId to string
            for doc in documents:
                doc["_id"] = str(doc["_id"])

            return documents

        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            raise HTTPException(status_code=500, detail="Error retrieving documents")

    @staticmethod
    async def delete_document(document_id: str, current_user: UserTenantDB) -> bool:
        """
        Delete a document from the knowledge base using async database.
        """
        try:
            from bson import ObjectId

            documents_collection = current_user.adb.documents
            result = await documents_collection.delete_one({
                "_id": ObjectId(document_id),
                "tenant_id": current_user.tenant_id
            })

            if result.deleted_count == 0:
                raise HTTPException(status_code=404, detail="Document not found")

            logger.info(f"Document {document_id} deleted by user {current_user.username}")
            return True

        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            raise HTTPException(status_code=500, detail="Error deleting document")

    @staticmethod
    async def insert_to_qdrant(collection_name: str, documents: List[dict], current_user: UserTenantDB) -> dict:
        """
        Insert documents directly into Qdrant collection.
        """
        try:
            # Initialize Qdrant only when needed
            await current_user.init_qdrant()

            from qdrant_client.models import Distance, VectorParams, PointStruct

            qdrant_client = current_user.qdrant

            # Check if collection exists, create if not
            collections = await qdrant_client.get_collections()
            collection_exists = any(col.name == collection_name for col in collections.collections)

            if not collection_exists:
                await qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(
                        size=1536,  # OpenAI embedding size
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created Qdrant collection: {collection_name}")

            # Prepare points for insertion
            points = []
            for doc in documents:
                # Generate dummy vector (replace with actual embeddings in production)
                vector = [0.0] * 1536

                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=vector,
                    payload=doc
                )
                points.append(point)

            # Insert points into Qdrant
            await qdrant_client.upsert(
                collection_name=collection_name,
                points=points
            )

            logger.info(f"Inserted {len(points)} documents into Qdrant collection: {collection_name}")

            return {
                "message": f"Successfully inserted {len(points)} documents into Qdrant",
                "collection": collection_name,
                "documents_inserted": len(points)
            }

        except Exception as e:
            logger.error(f"Error inserting to Qdrant: {e}")
            raise HTTPException(status_code=500, detail=f"Error inserting to Qdrant: {str(e)}")
