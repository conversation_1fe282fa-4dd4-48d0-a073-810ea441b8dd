# app/v1/services/knowledge_base/service.py

import fitz  # PyMuPDF
from typing import List
from fastapi import UploadFile, HTTPException
from datetime import datetime
from llama_index.core import Document, VectorStoreIndex, StorageContext
from llama_index.core.node_parser import Sen<PERSON>ceSplitter
from llama_index.embeddings.openai import OpenAIEmbedding
import hashlib

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class KnowledgeBaseService:
    """
    Simple service for handling PDF processing and knowledge base operations.
    """

    @staticmethod
    async def process_pdf_documents(files: List[UploadFile], current_user: UserTenantDB) -> dict:
        """
        Process PDF documents and create two collections:
        1. Whole documents (without embeddings)
        2. Split documents (with embeddings)
        """
        try:
            logger.info(f"User {current_user.username} is processing {len(files)} PDF documents")

            whole_documents = []
            split_documents = []

            for file in files:
                # Read PDF content
                pdf_content = await file.read()
                doc = fitz.open(stream=pdf_content, filetype="pdf")

                # Extract full text from PDF
                full_text = ""
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    full_text += page.get_text()

                # Create document ID
                doc_id = hashlib.sha256(f"{file.filename}_{current_user.tenant_id}".encode()).hexdigest()

                # Create whole document (without embeddings)
                whole_doc = Document(
                    text=full_text,
                    metadata={
                        "filename": file.filename,
                        "doc_id": doc_id,
                        "tenant_id": current_user.tenant_id,
                        "uploaded_by": current_user.username,
                        "uploaded_at": datetime.now().isoformat(),
                        "type": "whole_document"
                    }
                )
                whole_documents.append(whole_doc)

                # Create split documents (with embeddings)
                splitter = SentenceSplitter(
                    chunk_size=512,
                    chunk_overlap=50
                )

                # Split the document
                split_nodes = splitter.get_nodes_from_documents([whole_doc])

                # Add metadata to split nodes
                for i, node in enumerate(split_nodes):
                    node.metadata.update({
                        "chunk_id": f"{doc_id}_chunk_{i}",
                        "parent_doc_id": doc_id,
                        "chunk_index": i,
                        "type": "split_document"
                    })

                split_documents.extend(split_nodes)
                doc.close()

            # Store in collections
            await KnowledgeBaseService._store_in_collections(
                whole_documents,
                split_documents,
                current_user
            )

            # Store metadata in database
            await KnowledgeBaseService._store_document_metadata(files, current_user)

            logger.info(f"Successfully processed {len(files)} PDF files for user {current_user.username}")

            return {
                "message": f"Successfully processed {len(files)} PDF files",
                "whole_documents_created": len(whole_documents),
                "split_documents_created": len(split_documents),
                "files_processed": [file.filename for file in files],
                "processed_by": current_user.username,
                "tenant": current_user.tenant_name
            }

        except Exception as e:
            logger.error(f"Error processing PDF documents for user {current_user.username}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def _store_in_collections(whole_documents: List[Document], split_documents: List[Document], current_user: UserTenantDB):
        """
        Store documents in two separate collections:
        1. Whole documents without embeddings
        2. Split documents with embeddings
        """
        try:
            # Collection 1: Whole documents (without embeddings)
            whole_docs_collection = f"{current_user.tenant_slug}_whole_documents"

            # Create index without embeddings for whole documents
            whole_index = VectorStoreIndex.from_documents(
                whole_documents,
                embed_model=None,  # No embeddings
                show_progress=True
            )

            # Collection 2: Split documents (with embeddings)
            split_docs_collection = f"{current_user.tenant_slug}_split_documents"

            # Create index with embeddings for split documents
            embed_model = OpenAIEmbedding(
                model="text-embedding-3-small",
                dimensions=1536
            )

            split_index = VectorStoreIndex.from_documents(
                split_documents,
                embed_model=embed_model,
                show_progress=True
            )

            logger.info(f"Stored {len(whole_documents)} whole documents and {len(split_documents)} split documents")

            # TODO: Persist indexes to vector store (Qdrant, Pinecone, etc.)
            # For now, they're just in memory

        except Exception as e:
            logger.error(f"Error storing documents in collections: {e}")
            raise
    
    @staticmethod
    async def _store_document_metadata(files: List[UploadFile], current_user: UserTenantDB):
        """
        Store document metadata in the database.
        """
        try:
            documents_collection = current_user.db.documents
            
            for file in files:
                metadata = {
                    "filename": file.filename,
                    "file_size": file.size if hasattr(file, 'size') else 0,
                    "content_type": file.content_type,
                    "uploaded_at": datetime.now(),
                    "uploaded_by": current_user.username,
                    "tenant_id": current_user.tenant_id,
                    "processing_status": "completed"
                }
                
                documents_collection.insert_one(metadata)
                
        except Exception as e:
            logger.error(f"Error storing document metadata: {e}")
            # Don't raise exception here as document processing was successful
    
    @staticmethod
    async def get_documents(current_user: UserTenantDB, skip: int = 0, limit: int = 50) -> List[dict]:
        """
        Get list of documents for the current tenant.
        """
        try:
            documents_collection = current_user.db.documents
            documents = list(documents_collection.find().skip(skip).limit(limit).sort("uploaded_at", -1))
            
            # Convert ObjectId to string
            for doc in documents:
                doc["_id"] = str(doc["_id"])
                
            return documents
            
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            raise HTTPException(status_code=500, detail="Error retrieving documents")
    
    @staticmethod
    async def delete_document(document_id: str, current_user: UserTenantDB) -> bool:
        """
        Delete a document from the knowledge base.
        """
        try:
            from bson import ObjectId
            
            documents_collection = current_user.db.documents
            result = documents_collection.delete_one({
                "_id": ObjectId(document_id),
                "tenant_id": current_user.tenant_id
            })
            
            if result.deleted_count == 0:
                raise HTTPException(status_code=404, detail="Document not found")
                
            logger.info(f"Document {document_id} deleted by user {current_user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            raise HTTPException(status_code=500, detail="Error deleting document")
