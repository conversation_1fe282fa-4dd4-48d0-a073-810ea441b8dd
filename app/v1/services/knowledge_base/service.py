# app/v1/services/knowledge_base/service.py

from typing import List
from fastapi import UploadFile, HTTPException
from datetime import datetime

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from .models import DocumentUploadResponse, DocumentMetadata, ProcessingResult

logger = setup_new_logging(__name__)

class KnowledgeBaseService:
    """
    Service for handling document processing and knowledge base operations.
    """
    
    @staticmethod
    async def process_documents(files: List[UploadFile], current_user: UserTenantDB) -> DocumentUploadResponse:
        """
        Process documents and add them to the knowledge base.
        """
        try:
            logger.info(f"User {current_user.username} is processing {len(files)} documents")
            
            # Import here to avoid circular imports
            from app.knowledge_base.injest.file_processing import (
                process_files, 
                extract_text_and_images, 
                format_extracted_data, 
                create_nodes,
                handle_metadata
            )
            
            # Process files (convert to PDF if needed)
            processed_files = await process_files(files)
            
            # Extract text and images from PDFs
            extracted_data = await extract_text_and_images(processed_files, current_user.minio)
            
            # Format the extracted data
            formatted_data = format_extracted_data(extracted_data, current_user)
            
            # Create llama index nodes
            nodes = create_nodes(formatted_data)
            
            # Handle metadata (add presigned URLs)
            final_nodes = await handle_metadata(nodes, current_user.minio)
            
            # Store document metadata in database
            await KnowledgeBaseService._store_document_metadata(files, current_user)
            
            # TODO: Add nodes to your llama index collection here
            # index.insert_nodes(final_nodes)
            
            logger.info(f"Successfully processed {len(files)} files for user {current_user.username}")
            
            return DocumentUploadResponse(
                message=f"Successfully processed {len(files)} files",
                nodes_created=len(final_nodes),
                files_processed=[file.filename for file in files],
                processed_by=current_user.username,
                tenant=current_user.tenant_name
            )
            
        except Exception as e:
            logger.error(f"Error processing documents for user {current_user.username}: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def _store_document_metadata(files: List[UploadFile], current_user: UserTenantDB):
        """
        Store document metadata in the database.
        """
        try:
            documents_collection = current_user.db.documents
            
            for file in files:
                metadata = {
                    "filename": file.filename,
                    "file_size": file.size if hasattr(file, 'size') else 0,
                    "content_type": file.content_type,
                    "uploaded_at": datetime.now(),
                    "uploaded_by": current_user.username,
                    "tenant_id": current_user.tenant_id,
                    "processing_status": "completed"
                }
                
                documents_collection.insert_one(metadata)
                
        except Exception as e:
            logger.error(f"Error storing document metadata: {e}")
            # Don't raise exception here as document processing was successful
    
    @staticmethod
    async def get_documents(current_user: UserTenantDB, skip: int = 0, limit: int = 50) -> List[dict]:
        """
        Get list of documents for the current tenant.
        """
        try:
            documents_collection = current_user.db.documents
            documents = list(documents_collection.find().skip(skip).limit(limit).sort("uploaded_at", -1))
            
            # Convert ObjectId to string
            for doc in documents:
                doc["_id"] = str(doc["_id"])
                
            return documents
            
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            raise HTTPException(status_code=500, detail="Error retrieving documents")
    
    @staticmethod
    async def delete_document(document_id: str, current_user: UserTenantDB) -> bool:
        """
        Delete a document from the knowledge base.
        """
        try:
            from bson import ObjectId
            
            documents_collection = current_user.db.documents
            result = documents_collection.delete_one({
                "_id": ObjectId(document_id),
                "tenant_id": current_user.tenant_id
            })
            
            if result.deleted_count == 0:
                raise HTTPException(status_code=404, detail="Document not found")
                
            logger.info(f"Document {document_id} deleted by user {current_user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            raise HTTPException(status_code=500, detail="Error deleting document")
