# app/v1/services/knowledge_base/service.py

import fitz  # PyMuPDF
from typing import List
from fastapi import UploadFile, HTTPException
from datetime import datetime
from llama_index.core import Document
import hashlib
import re

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from .splitters import SentenceSplitter

logger = setup_new_logging(__name__)

class KnowledgeBaseService:
    """
    Simple service for handling PDF processing and knowledge base operations.
    """

    @staticmethod
    async def process_pdf_documents(files: List[UploadFile], current_user: UserTenantDB) -> dict:
        """
        Process Nepali PDF documents and create two collections:
        1. Whole documents (without embeddings)
        2. Split documents (with embeddings using custom splitter)
        """
        try:
            logger.info(f"User {current_user.username} is processing {len(files)} Nepali PDF documents")

            whole_documents = []
            split_documents = []

            for file in files:
                # Read PDF content
                pdf_content = await file.read()
                doc = fitz.open(stream=pdf_content, filetype="pdf")

                # Extract full text from PDF with proper Nepali text handling
                full_text = ""
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    # Get text with proper encoding for Nepali
                    page_text = page.get_text("text", flags=fitz.TEXT_PRESERVE_WHITESPACE)
                    full_text += page_text + "\n"

                # Clean and normalize Nepali text
                full_text = KnowledgeBaseService._clean_nepali_text(full_text)

                # Create document ID
                doc_id = hashlib.sha256(f"{file.filename}_{current_user.tenant_id}".encode()).hexdigest()

                # Create whole document (without embeddings)
                whole_doc = Document(
                    text=full_text,
                    metadata={
                        "filename": file.filename,
                        "doc_id": doc_id,
                        "tenant_id": current_user.tenant_id,
                        "uploaded_by": current_user.username,
                        "uploaded_at": datetime.now().isoformat(),
                        "type": "whole_document",
                        "language": "nepali"
                    }
                )
                whole_documents.append(whole_doc)

                # Create split documents using custom splitter (like echo_bot)
                splitter = SentenceSplitter(
                    max_chunk_length=400,  # Smaller chunks for Nepali
                    overlap_sentences=1,
                    min_sentences_per_chunk=3
                )

                # Split the document using custom splitter
                split_nodes = splitter([whole_doc])

                # Add metadata to split nodes
                for i, node in enumerate(split_nodes):
                    node.metadata.update({
                        "chunk_id": f"{doc_id}_chunk_{i}",
                        "parent_doc_id": doc_id,
                        "chunk_index": i,
                        "type": "split_document",
                        "language": "nepali"
                    })

                split_documents.extend(split_nodes)
                doc.close()

            # Store in collections
            await KnowledgeBaseService._store_in_collections(
                whole_documents,
                split_documents,
                current_user
            )

            # Store metadata in database
            await KnowledgeBaseService._store_document_metadata(files, current_user)

            logger.info(f"Successfully processed {len(files)} Nepali PDF files for user {current_user.username}")

            return {
                "message": f"Successfully processed {len(files)} Nepali PDF files",
                "whole_documents_created": len(whole_documents),
                "split_documents_created": len(split_documents),
                "files_processed": [file.filename for file in files],
                "processed_by": current_user.username,
                "tenant": current_user.tenant_name
            }

        except Exception as e:
            logger.error(f"Error processing Nepali PDF documents for user {current_user.username}: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    def _clean_nepali_text(text: str) -> str:
        """
        Clean and normalize Nepali text extracted from PDF.
        """
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove empty lines
        text = re.sub(r'\n\s*\n', '\n', text)

        # Normalize Nepali characters (if needed)
        # Add specific Nepali text normalization here

        # Remove leading/trailing whitespace
        text = text.strip()

        return text

    @staticmethod
    async def _store_in_collections(whole_documents: List[Document], split_documents: List[Document], current_user: UserTenantDB):
        """
        Store documents in two separate collections:
        1. Whole documents without embeddings
        2. Split documents with embeddings
        """
        try:
            # Collection names
            whole_docs_collection = f"{current_user.tenant_slug}_whole_documents"
            split_docs_collection = f"{current_user.tenant_slug}_split_documents"

            logger.info(f"Preparing to store {len(whole_documents)} whole documents and {len(split_documents)} split documents")

            # TODO: Implement actual storage to vector database
            # For now, just log the collections that would be created
            logger.info(f"Would create collections: {whole_docs_collection}, {split_docs_collection}")

            # Collection 1: Store whole documents (without embeddings)
            # This would typically go to a document store or vector DB without embeddings
            for doc in whole_documents:
                logger.debug(f"Whole document: {doc.metadata['filename']} - {len(doc.text)} characters")

            # Collection 2: Store split documents (with embeddings)
            # This would typically go to a vector DB with embeddings
            for doc in split_documents:
                logger.debug(f"Split document: {doc.metadata['chunk_id']} - {len(doc.text)} characters")

            logger.info(f"Successfully prepared storage for {len(whole_documents)} whole documents and {len(split_documents)} split documents")

        except Exception as e:
            logger.error(f"Error storing documents in collections: {e}")
            raise
    
    @staticmethod
    async def _store_document_metadata(files: List[UploadFile], current_user: UserTenantDB):
        """
        Store document metadata in the database.
        """
        try:
            documents_collection = current_user.db.documents
            
            for file in files:
                metadata = {
                    "filename": file.filename,
                    "file_size": file.size if hasattr(file, 'size') else 0,
                    "content_type": file.content_type,
                    "uploaded_at": datetime.now(),
                    "uploaded_by": current_user.username,
                    "tenant_id": current_user.tenant_id,
                    "processing_status": "completed"
                }
                
                documents_collection.insert_one(metadata)
                
        except Exception as e:
            logger.error(f"Error storing document metadata: {e}")
            # Don't raise exception here as document processing was successful
    
    @staticmethod
    async def get_documents(current_user: UserTenantDB, skip: int = 0, limit: int = 50) -> List[dict]:
        """
        Get list of documents for the current tenant.
        """
        try:
            documents_collection = current_user.db.documents
            documents = list(documents_collection.find().skip(skip).limit(limit).sort("uploaded_at", -1))
            
            # Convert ObjectId to string
            for doc in documents:
                doc["_id"] = str(doc["_id"])
                
            return documents
            
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            raise HTTPException(status_code=500, detail="Error retrieving documents")
    
    @staticmethod
    async def delete_document(document_id: str, current_user: UserTenantDB) -> bool:
        """
        Delete a document from the knowledge base.
        """
        try:
            from bson import ObjectId
            
            documents_collection = current_user.db.documents
            result = documents_collection.delete_one({
                "_id": ObjectId(document_id),
                "tenant_id": current_user.tenant_id
            })
            
            if result.deleted_count == 0:
                raise HTTPException(status_code=404, detail="Document not found")
                
            logger.info(f"Document {document_id} deleted by user {current_user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            raise HTTPException(status_code=500, detail="Error deleting document")
