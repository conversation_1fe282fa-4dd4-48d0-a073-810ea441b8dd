# app/v1/services/knowledge_base/routes.py

from fastapi import APIRouter, UploadFile, File, Depends, HTTPException, Query
from typing import List
from datetime import datetime

from app.shared.database.models import UserTenantDB
from app.shared.security.permissions import require_permission, admin_only
from app.shared.utils.logger import setup_new_logging
from .models import DocumentUploadResponse
from .service import KnowledgeBaseService

router = APIRouter(prefix="/knowledge-base", tags=["Knowledge Base"])
logger = setup_new_logging(__name__)

@router.post("/upload-pdfs", response_model=DocumentUploadResponse)
async def upload_pdfs_to_index(
    files: List[UploadFile] = File(...),
    current_user: UserTenantDB = Depends(require_permission("documents", "create"))
):
    """
    Upload PDF files to the knowledge base index.
    Requires 'create' permission on 'documents' resource.
    """
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    
    # Validate file types
    for file in files:
        if not file.filename.lower().endswith(('.pdf', '.docx', '.doc', '.txt')):
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type: {file.filename}. Only PDF, DOCX, DOC, and TXT files are allowed."
            )
    
    return await KnowledgeBaseService.process_documents(files, current_user)

@router.post("/process-documents", response_model=DocumentUploadResponse)
async def process_documents(
    files: List[UploadFile] = File(...),
    current_user: UserTenantDB = Depends(require_permission("documents", "create"))
):
    """
    Process documents and add them to the knowledge base.
    This is the main endpoint for document processing with authentication.
    Requires 'create' permission on 'documents' resource.
    """
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    
    # Validate file types
    for file in files:
        if not file.filename.lower().endswith(('.pdf', '.docx', '.doc', '.txt')):
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type: {file.filename}. Only PDF, DOCX, DOC, and TXT files are allowed."
            )
    
    return await KnowledgeBaseService.process_documents(files, current_user)

@router.get("/documents")
async def get_documents(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: UserTenantDB = Depends(require_permission("documents", "read"))
):
    """
    Get list of documents in the knowledge base.
    Requires 'read' permission on 'documents' resource.
    """
    return await KnowledgeBaseService.get_documents(current_user, skip, limit)

@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: str,
    current_user: UserTenantDB = Depends(require_permission("documents", "delete"))
):
    """
    Delete a document from the knowledge base.
    Requires 'delete' permission on 'documents' resource.
    """
    success = await KnowledgeBaseService.delete_document(document_id, current_user)
    if success:
        return {"message": "Document deleted successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to delete document")

@router.get("/stats")
async def get_knowledge_base_stats(
    current_user: UserTenantDB = Depends(require_permission("documents", "read"))
):
    """
    Get knowledge base statistics for the current tenant.
    Requires 'read' permission on 'documents' resource.
    """
    try:
        documents_collection = current_user.db.documents
        
        total_documents = documents_collection.count_documents({"tenant_id": current_user.tenant_id})
        recent_documents = documents_collection.count_documents({
            "tenant_id": current_user.tenant_id,
            "uploaded_at": {"$gte": datetime.now().replace(hour=0, minute=0, second=0)}
        })
        
        return {
            "total_documents": total_documents,
            "recent_documents": recent_documents,
            "tenant_name": current_user.tenant_name
        }
        
    except Exception as e:
        logger.error(f"Error getting knowledge base stats: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving statistics")
