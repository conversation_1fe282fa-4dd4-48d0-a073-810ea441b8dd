# app/v1/services/knowledge_base/routes.py

from fastapi import APIRouter, UploadFile, File, Depends, HTTPException
from typing import List

from app.shared.database.models import UserTenantDB
from app.shared.security.permissions import require_permission
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging
from .service import KnowledgeBaseService

router = APIRouter(prefix="/knowledge-base", tags=["Knowledge Base"])
logger = setup_new_logging(__name__)

@router.post("/process-documents")
async def process_documents(
    files: List[UploadFile] = File(...),
    current_user: UserTenantDB = Depends(require_permission("documents", "create"))
):
    """
    Process PDF documents and add to LlamaIndex collections.
    Collection names are retrieved from MongoDB settings.
    Creates two collections:
    1. Whole documents collection (without embeddings)
    2. Split documents collection (with embeddings)
    """
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")

    # Validate only PDF files
    for file in files:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400,
                detail=f"Only PDF files are supported. Got: {file.filename}"
            )

    return await KnowledgeBaseService.process_documents_to_llamaindex(files, current_user)
