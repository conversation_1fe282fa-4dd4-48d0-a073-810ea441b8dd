# app/v1/services/knowledge_base/routes.py

from fastapi import APIRouter, UploadFile, File, Depends, HTTPException, Query
from typing import List
from datetime import datetime

from app.shared.database.models import UserTenantDB
from app.shared.security.permissions import require_permission
from app.shared.utils.logger import setup_new_logging
from .service import KnowledgeBaseService

router = APIRouter(prefix="/knowledge-base", tags=["Knowledge Base"])
logger = setup_new_logging(__name__)

@router.post("/process-documents")
async def process_documents(
    files: List[UploadFile] = File(...),
    current_user: UserTenantDB = Depends(require_permission("documents", "create"))
):
    """
    Process PDF documents and add to LlamaIndex collections.
    Collection names are retrieved from MongoDB settings.
    Creates two collections:
    1. Whole documents collection (without embeddings)
    2. Split documents collection (with embeddings)
    """
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")

    # Validate only PDF files
    for file in files:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400,
                detail=f"Only PDF files are supported. Got: {file.filename}"
            )

    return await KnowledgeBaseService.process_documents_to_llamaindex(files, current_user)

@router.get("/documents")
async def get_documents(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: UserTenantDB = Depends(require_permission("documents", "read"))
):
    """
    Get list of documents in the knowledge base.
    Requires 'read' permission on 'documents' resource.
    """
    return await KnowledgeBaseService.get_documents(current_user, skip, limit)

@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: str,
    current_user: UserTenantDB = Depends(require_permission("documents", "delete"))
):
    """
    Delete a document from the knowledge base.
    Requires 'delete' permission on 'documents' resource.
    """
    success = await KnowledgeBaseService.delete_document(document_id, current_user)
    if success:
        return {"message": "Document deleted successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to delete document")

@router.get("/stats")
async def get_knowledge_base_stats(
    current_user: UserTenantDB = Depends(require_permission("documents", "read"))
):
    """
    Get knowledge base statistics for the current tenant.
    Requires 'read' permission on 'documents' resource.
    """
    try:
        # Use async database
        documents_collection = current_user.adb.documents

        total_documents = await documents_collection.count_documents({"tenant_id": current_user.tenant_id})
        recent_documents = await documents_collection.count_documents({
            "tenant_id": current_user.tenant_id,
            "uploaded_at": {"$gte": datetime.now().replace(hour=0, minute=0, second=0)}
        })
        
        return {
            "total_documents": total_documents,
            "recent_documents": recent_documents,
            "tenant_name": current_user.tenant_name
        }
        
    except Exception as e:
        logger.error(f"Error getting knowledge base stats: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving statistics")
