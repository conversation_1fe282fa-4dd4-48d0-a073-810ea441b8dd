# app/v1/services/knowledge_base/splitters.py

import re
import uuid
from typing import List, Dict
from llama_index.core import Document
from llama_index.core.schema import TransformComponent
import nltk
from nltk.tokenize import sent_tokenize

from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

# Download NLTK data if not already present
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    logger.info("Downloading NLTK punkt tokenizer...")
    nltk.download('punkt')

class SentenceSplitter(TransformComponent):
    """
    Custom sentence splitter that creates chunks based on sentence boundaries
    with configurable overlap and minimum sentences per chunk.
    """
    
    def __init__(self, max_chunk_length: int = 300, overlap_sentences: int = 1, min_sentences_per_chunk: int = 5):
        """
        Initialize the sentence splitter.
        
        Args:
            max_chunk_length: Maximum character length per chunk
            overlap_sentences: Number of sentences to overlap between chunks
            min_sentences_per_chunk: Minimum number of sentences per chunk
        """
        super().__init__()
        self.max_chunk_length = max_chunk_length
        self.overlap_sentences = overlap_sentences
        self.min_sentences_per_chunk = min_sentences_per_chunk

    def __call__(self, documents: List[Document], **kwargs) -> List[Document]:
        """
        Split documents into smaller chunks based on sentence boundaries.
        
        Args:
            documents: List of Document objects to split
            
        Returns:
            List of Document objects with split text
        """
        logger.info(f"Splitting {len(documents)} documents into sentence-based chunks")
        nodes = []
        
        for doc in documents:
            doc_text = doc.text
            grouped_sentences = self._split_text(doc_text)
            
            # Create new documents for each chunk
            for chunk in grouped_sentences:
                chunk_doc = Document(
                    text=chunk['text'], 
                    metadata={
                        **doc.metadata, 
                        'chunk_id': chunk['id'],
                        'original_doc_id': doc.metadata.get('hash_id', str(uuid.uuid4()))
                    }
                )
                nodes.append(chunk_doc)
        
        logger.info(f"Created {len(nodes)} chunks from {len(documents)} documents")
        return nodes

    def _split_text(self, text: str) -> List[Dict[str, str]]:
        """
        Split text into chunks based on sentence boundaries.
        
        Args:
            text: Text to split
            
        Returns:
            List of dictionaries containing chunk id and text
        """
        sentences = sent_tokenize(text)
        chunks = []

        current_index = 0

        while current_index < len(sentences):
            current_chunk_sentences = []
            current_chunk_text = ""

            # Determine max sentences for this chunk based on overlap
            if chunks:
                max_sentences = self.min_sentences_per_chunk - self.overlap_sentences
            else:
                max_sentences = self.min_sentences_per_chunk

            while current_index < len(sentences):
                next_sentence = sentences[current_index]
                
                # Check character limit
                if len(current_chunk_text + " " + next_sentence) > self.max_chunk_length:
                    # If not enough sentences yet, add anyway
                    if len(current_chunk_sentences) < max_sentences:
                        current_chunk_sentences.append(next_sentence)
                        current_chunk_text += " " + next_sentence
                        current_index += 1
                        continue
                    else:
                        break  # Exceeded length but met min sentences

                # Add sentence and check if we've reached max sentences
                current_chunk_sentences.append(next_sentence)
                current_chunk_text += " " + next_sentence
                current_index += 1

                if len(current_chunk_sentences) >= max_sentences:
                    break  # Stop once we've met the required number

            # Append the chunk
            chunk_id = str(uuid.uuid4())
            chunks.append({"id": chunk_id, "text": current_chunk_text.strip()})

            # Handle overlap with previous chunk
            if len(chunks) > 1:
                # Get overlap from previous chunk
                prev_chunk_sentences = sent_tokenize(chunks[-2]["text"])
                overlap = prev_chunk_sentences[-self.overlap_sentences:]

                # Prepend overlap to current chunk's text
                new_text = " ".join(overlap) + " " + chunks[-1]["text"]
                new_sentences = sent_tokenize(new_text)
                new_text = new_text.replace("\n", " ")
                
                # Check constraints again after adding overlap
                if (len(new_text) <= self.max_chunk_length and 
                    len(new_sentences) >= self.min_sentences_per_chunk):
                    chunks[-1]["text"] = new_text.strip()

        # Special handling for last chunk (if needed)
        if len(chunks) > 1:
            last_chunk = chunks[-1]
            second_last = chunks[-2]
            last_sentences = sent_tokenize(last_chunk["text"])
            
            if len(last_sentences) < self.min_sentences_per_chunk:
                merged_text = second_last["text"] + " " + last_chunk["text"]
                merged_sentences = sent_tokenize(merged_text)
                
                if len(merged_text) <= self.max_chunk_length and len(merged_sentences) <= self.min_sentences_per_chunk:
                    second_last["text"] = merged_text
                    chunks.pop()

        return chunks

class CharacterSplitter:
    """
    Simple character-based splitter with overlap.
    """
    
    def __init__(self, chunk_size: int = 1000, overlap_size: int = 200):
        """
        Initialize the character splitter.
        
        Args:
            chunk_size: Maximum characters per chunk
            overlap_size: Number of characters to overlap between chunks
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size

    def __call__(self, documents: List[Document], **kwargs) -> List[Document]:
        """
        Split documents into character-based chunks.
        
        Args:
            documents: List of Document objects to split
            
        Returns:
            List of Document objects with split text
        """
        logger.info(f"Splitting {len(documents)} documents into character-based chunks")
        nodes = []
        
        for doc in documents:
            doc_text = doc.text
            chunks = self._split_text(doc_text)
            
            # Create new documents for each chunk
            for chunk in chunks:
                chunk_doc = Document(
                    text=chunk['text'], 
                    metadata={
                        **doc.metadata, 
                        'chunk_id': chunk['id'],
                        'original_doc_id': doc.metadata.get('hash_id', str(uuid.uuid4()))
                    }
                )
                nodes.append(chunk_doc)
        
        logger.info(f"Created {len(nodes)} chunks from {len(documents)} documents")
        return nodes

    def _split_text(self, text: str) -> List[Dict[str, str]]:
        """
        Split text into character-based chunks with overlap.
        
        Args:
            text: Text to split
            
        Returns:
            List of dictionaries containing chunk id and text
        """
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.chunk_size
            chunk_text = text[start:end]
            
            chunk = {
                "text": chunk_text, 
                "id": str(uuid.uuid4())
            }
            chunks.append(chunk)
            
            start = end - self.overlap_size  # Set the next start to overlap
        
        return chunks

class RecursiveCharacterSplitter:
    """
    Recursive character splitter that tries to split on natural boundaries.
    """
    
    def __init__(self, chunk_size: int = 1000, overlap_size: int = 200, separators: List[str] = None):
        """
        Initialize the recursive character splitter.
        
        Args:
            chunk_size: Maximum characters per chunk
            overlap_size: Number of characters to overlap between chunks
            separators: List of separators to try in order (default: ["\n\n", "\n", " ", ""])
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size
        self.separators = separators or ["\n\n", "\n", " ", ""]

    def __call__(self, documents: List[Document], **kwargs) -> List[Document]:
        """
        Split documents using recursive character splitting.
        
        Args:
            documents: List of Document objects to split
            
        Returns:
            List of Document objects with split text
        """
        logger.info(f"Splitting {len(documents)} documents using recursive character splitting")
        nodes = []
        
        for doc in documents:
            doc_text = doc.text
            chunks = self._split_text(doc_text)
            
            # Create new documents for each chunk
            for chunk in chunks:
                chunk_doc = Document(
                    text=chunk['text'], 
                    metadata={
                        **doc.metadata, 
                        'chunk_id': chunk['id'],
                        'original_doc_id': doc.metadata.get('hash_id', str(uuid.uuid4()))
                    }
                )
                nodes.append(chunk_doc)
        
        logger.info(f"Created {len(nodes)} chunks from {len(documents)} documents")
        return nodes

    def _split_text(self, text: str) -> List[Dict[str, str]]:
        """
        Recursively split text using different separators.
        
        Args:
            text: Text to split
            
        Returns:
            List of dictionaries containing chunk id and text
        """
        return self._split_text_recursive(text, self.separators)

    def _split_text_recursive(self, text: str, separators: List[str]) -> List[Dict[str, str]]:
        """
        Recursively split text using the provided separators.
        """
        if len(text) <= self.chunk_size:
            return [{"text": text, "id": str(uuid.uuid4())}]

        if not separators:
            # Fallback to character splitting
            return self._character_split(text)

        separator = separators[0]
        remaining_separators = separators[1:]

        if separator == "":
            # Character-level splitting
            return self._character_split(text)

        splits = text.split(separator)
        chunks = []
        current_chunk = ""

        for split in splits:
            if len(current_chunk + separator + split) <= self.chunk_size:
                current_chunk += separator + split if current_chunk else split
            else:
                if current_chunk:
                    chunks.extend(self._split_text_recursive(current_chunk, remaining_separators))
                current_chunk = split

        if current_chunk:
            chunks.extend(self._split_text_recursive(current_chunk, remaining_separators))

        return chunks

    def _character_split(self, text: str) -> List[Dict[str, str]]:
        """
        Split text at character level with overlap.
        """
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.chunk_size
            chunk_text = text[start:end]
            
            chunk = {
                "text": chunk_text, 
                "id": str(uuid.uuid4())
            }
            chunks.append(chunk)
            
            start = end - self.overlap_size
        
        return chunks
