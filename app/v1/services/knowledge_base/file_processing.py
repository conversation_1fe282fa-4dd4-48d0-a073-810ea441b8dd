# app/v1/services/knowledge_base/file_processing.py

import os
import asyncio
import fitz
import pdfkit
from typing import Optional, List, Dict
from datetime import timedelta, datetime
from docx import Document as Docx_bytes
from fastapi import HTTPException, UploadFile
from io import BytesIO
from llama_index.core.schema import Document
import hashlib
import re
from collections import deque

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

async def convert_txt_to_pdf(text: str) -> bytes:
    """Convert a text string to a PDF (as bytes)."""
    html_content = f"<html><body><pre>{text}</pre></body></html>"
    return await asyncio.to_thread(pdfkit.from_string, html_content, False)

async def convert_docx_to_pdf(docx_content: bytes) -> bytes:
    """Convert a DOCX file (as bytes) to a PDF (as bytes)."""
    doc = Docx_bytes(BytesIO(docx_content))
    text = "\n".join([p.text for p in doc.paragraphs])
    return await convert_txt_to_pdf(text)

async def process_files(files: Optional[List[UploadFile]]) -> List[UploadFile]:
    """
    Process uploaded files and convert them to PDF format.
    """
    if not files:
        return []
    
    logger.info(f"Processing files: {[file.filename for file in files]}")
    
    pdf_bytes = []

    for file in files:
        file_ext = os.path.splitext(file.filename)[1].lower()
        file_content = await file.read()
        
        if file_ext == ".pdf":
            pdf_bytes.append(UploadFile(
                filename=file.filename, 
                file=BytesIO(file_content), 
                headers={"Content-Type": "application/pdf"}
            ))
        elif file_ext == ".txt":
            pdf_byte = await convert_txt_to_pdf(file_content.decode('utf-8'))
            pdf_bytes.append(UploadFile(
                filename=file.filename, 
                file=BytesIO(pdf_byte), 
                headers={"Content-Type": "application/pdf"}
            ))
        elif file_ext == ".docx":
            pdf_byte = await convert_docx_to_pdf(file_content)
            pdf_bytes.append(UploadFile(
                filename=file.filename, 
                file=BytesIO(pdf_byte), 
                headers={"Content-Type": "application/pdf"}
            ))
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_ext}")
    
    return pdf_bytes

async def extract_text_and_images(pdfs: List[UploadFile], minio_client=None) -> List[Dict]:
    """
    Extract text and images from PDF files.
    """
    results = []
    
    for pdf in pdfs:
        doc_bytes = await pdf.read()
        doc = fitz.open(stream=doc_bytes, filetype="pdf")
        pdf_name = pdf.filename
        
        # Upload to MinIO if client is available
        minio_file_name = pdf_name
        if minio_client:
            minio_file_name = minio_client.upload_bytes(pdf_name, doc_bytes, "Files")
        
        pdf_data = []
        prev_text_blocks = deque()  # Store previous short text blocks
        prev_images = deque()  # Store images from pages with little/no text
        processed_images = set()

        for page in doc:
            page_data = {
                "page_number": page.number + 1,
                "text_blocks": [],
                "images": []
            }

            # Extract text blocks
            text_blocks = []
            for block in page.get_text("blocks"):
                if block[6] == 0:  # Text block
                    text = block[4].strip().rstrip("-")  # Remove trailing hyphens
                    text = " ".join(text.split())  # Normalize spaces
                    if text and len(text) < 100:  
                        prev_text_blocks.append(text)  # Store short text
                    else:
                        if prev_text_blocks:
                            text = " ".join(prev_text_blocks) + " " + text
                            prev_text_blocks.clear()  # Merge and reset
                        
                        text_blocks.append({
                            "text": text,
                            "bbox": list(fitz.Rect(block[:4]))
                        })
            
            # Extract images
            images = []
            for img_index, img in enumerate(page.get_images(full=True)):
                if img in processed_images:
                    continue
                    
                processed_images.add(img)
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_path = f"page{page.number+1}_img{img_index}.{base_image['ext']}"
                
                # Upload image to MinIO if client is available
                if minio_client:
                    minio_client.upload_bytes(
                        image_path,
                        base_image["image"],
                        f"Images/{pdf_name}"
                    )
                
                img_data = {
                    "path": image_path,
                    "bbox": list(page.get_image_bbox(img))
                }
                images.append(img_data)

            # If no text or all text is too short, store images in prev_images
            if not text_blocks or sum(len(tb["text"]) for tb in text_blocks) < 50:
                prev_images.extend(images)
            else:
                # Merge previous short texts and images into this page
                if prev_text_blocks:
                    text_blocks.insert(0, {
                        "text": " ".join(prev_text_blocks),
                        "bbox": []
                    })
                    prev_text_blocks.clear()

                if prev_images:
                    images.extend(prev_images)
                    prev_images.clear()

            # Add final text and images to page data
            page_data["text_blocks"] = text_blocks
            page_data["images"] = images
            pdf_data.append(page_data)
        
        results.append({"document": minio_file_name, "pages": pdf_data})
    
    return results

def format_extracted_data(raw_data: List[Dict], current_user: UserTenantDB) -> List[Dict]:
    """
    Format extracted data into a structured format for node creation.
    """
    formatted = []
    created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    for doc in raw_data:
        created_at = (datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S") + timedelta(seconds=1)).strftime("%Y-%m-%d %H:%M:%S")
        pdf_name = os.path.basename(doc["document"])
        
        for page in doc["pages"]:
            text = " ".join([b["text"] for b in page["text_blocks"]])
            if len(text) < 50:
                continue
                
            formatted.append({
                "text": text,
                "metadata": {
                    "page_number": page["page_number"],
                    "images": [img["path"] for img in page["images"]],
                    "source": pdf_name,
                    "hash_id": hashlib.sha256(f"{pdf_name}_{page['page_number']}".encode()).hexdigest(),
                    "updated_by": current_user.id,
                    "tenant_id": current_user.tenant_id,
                    "created_at": created_at,
                    "updated_at": created_at
                }
            })
    return formatted

def create_nodes(formatted_data: List[Dict]) -> List[Document]:
    """
    Create LlamaIndex Document nodes from formatted data.
    """
    nodes = []
    for doc in formatted_data:
        node = Document(
            text=doc.get("text"), 
            metadata=doc.get("metadata")
        )
        nodes.append(node)
    return nodes

async def handle_metadata(nodes: List[Document], minio_client=None) -> List[Document]:
    """
    Handle metadata processing and add presigned URLs for files and images.
    """
    results = []
    
    for node in nodes:
        # Remove embedding fields from metadata as they are too big for JSON output
        node_metadata = node.metadata
        new_node_metadata = {key: val for key, val in node_metadata.items() if "embedding" not in key}
        
        # Create new node with cleaned metadata
        node_dict = node.dict()
        node_dict["metadata"] = new_node_metadata
        node_dict["text"] = node.text
        new_node = Document(**node_dict)
        
        # Add presigned URLs if MinIO client is available
        if minio_client:
            file = new_node.metadata.get("source")
            if file:
                try:
                    presigned_url = minio_client.get_presigned_url(file, "Files")
                    new_node.metadata["source_url"] = presigned_url
                except Exception as e:
                    logger.warning(f"Could not get presigned URL for {file}: {e}")

            # Handle images
            images = new_node.metadata.get("images", [])
            if images:
                try:
                    presigned_urls = []
                    for image in images:
                        presigned_url = minio_client.get_presigned_url(image, f"Images/{file}")
                        presigned_urls.append({"name": image, "url": presigned_url})
                    new_node.metadata["images_url"] = presigned_urls
                except Exception as e:
                    logger.warning(f"Could not get presigned URLs for images: {e}")

        results.append(new_node)
    
    return results
