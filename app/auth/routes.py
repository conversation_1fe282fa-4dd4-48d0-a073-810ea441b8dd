# app/auth/routes.py

import os
from fastapi import APIRouter, HTTPException, Depends, status
from pymongo.errors import PyMongoError
import jwt
from datetime import datetime, timedelta, timezone

from app.core.security import (
    create_access_token,
    verify_password,
    hash_password,
    create_invitation_token,
    verify_invitation_token,
    get_tenant_info,
    min_role,
    SECRET_KEY,
    ALGORITHM
)
from app.core.database import get_db_from_tenant_id, get_tenant_id_and_name_from_slug
from app.models.auth import (
    OAuth2PasswordRequestFormWithClientID,
    ChangePasswordRequest,
    ResetPasswordRequest,
    ExtendedTokenRequest,
    get_login_form_with_referrer_check,
    UserRegistration,
    UserInvitation,
    UserRegistrationViaInvitation,
    LoginResponse
)
from app.models.user import UserTenantDB
from app.helper.logger import setup_new_logging

loggers = setup_new_logging(__name__)

router = APIRouter(tags=["Authentication"])

def convert_objectid_to_str(obj):
    """Convert ObjectId to string for JSON serialization."""
    if isinstance(obj, dict):
        return {k: str(v) if hasattr(v, '__str__') and 'ObjectId' in str(type(v)) else v for k, v in obj.items()}
    return obj

@router.get("/get_tenant_id")
async def tenantid_from_slug(slug: str):
    """
    Get tenant ID and name from slug.
    """
    try:
        result = get_tenant_id_and_name_from_slug(slug)
        if not result:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Tenant not found")

        # Check if tenant is active
        if not result.get("is_active", True):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Tenant is inactive")

        tenant_id = str(result["_id"])
        tenant_name = result["name"]

        return {
            "tenant_id": tenant_id,
            "tenant_name": tenant_name,
            "tenant_label": result.get("label", tenant_name),
            "tenant_slug": result["slug"]
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        loggers.error(f"Error getting tenant ID: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/login", response_model=LoginResponse)
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends(get_login_form_with_referrer_check)):
    """
    Login endpoint with Argon2 password verification.
    """
    loggers.info(f"Processing login with client_id: {form_data.client_id}")

    # Find the database name of that tenant
    result = get_tenant_id_and_name_from_slug(form_data.client_id)
    if not result:
        raise HTTPException(status_code=404, detail="Tenant not found")
    
    tenant_id = str(result["_id"])
    tenant_database = get_db_from_tenant_id(tenant_id)
    
    # Connect to the user_collection of that database
    user = tenant_database.users.find_one({"username": form_data.username})
    
    if not user:
        raise HTTPException(status_code=401, detail="User not found")

    # Verify password using Argon2
    if not verify_password(form_data.password, user["hashed_password"]):
        raise HTTPException(status_code=401, detail="Incorrect Credentials")

    # Get navigation permissions
    try:
        nav_permission = tenant_database.settings.find_one({"name": "nav_permission"})
        nav_permission = nav_permission.get(user["role"]) if nav_permission else None
    except Exception as e:
        loggers.warning(f"Could not get nav permissions: {e}")
        nav_permission = None

    # Get token validity settings
    days = 0
    hours = 0
    minutes = 0
    seconds = 0
    
    try:
        token_settings = tenant_database.settings.find_one({"name": "token_validity"})
        if token_settings:
            days = token_settings.get("days", 0)
            hours = token_settings.get("hours", 0)
            minutes = token_settings.get("minutes", 0)
            seconds = token_settings.get("seconds", 0)

        expires_delta = timedelta(
            days=days,
            hours=hours,
            minutes=minutes,
            seconds=seconds
        )
        
        loggers.info(f"Token validity settings: {token_settings}")

        # If no time components were found, use default
        if days == 0 and hours == 0 and minutes == 0 and seconds == 0:
            loggers.warning("No token validity settings found. Using default (6 hours).")
            expires_delta = timedelta(hours=6)  # Default: 6 hours

    except Exception as e:
        # Fallback to 6 hours if any error occurs
        expires_delta = timedelta(hours=6)
        loggers.warning(f"Error fetching token validity settings: {str(e)}. Using default (6 hours).")

    # Create access token
    access_token = create_access_token(
        data={"sub": user["username"], "user_id": str(user["_id"]), "role": user["role"], "tenant_id": tenant_id},
        expires_delta=expires_delta
    )

    # Calculate expiration date for response
    expiration_date = datetime.now(timezone.utc) + expires_delta

    user = convert_objectid_to_str(user)

    return LoginResponse(
        id=user["_id"],
        access_token=access_token,
        token_type="bearer",
        username=user['username'],
        role=user['role'],
        tenant_id=tenant_id,
        tenant_label=result["label"] if "label" in result else result["name"],
        tenant_slug=form_data.client_id,
        nav_permission=nav_permission,
        token_validity={
            "days": days,
            "hours": hours,
            "minutes": minutes,
            "seconds": seconds,
            "total_seconds": expires_delta.total_seconds()
        },
        expires_at=expiration_date.isoformat()
    )

@router.get("/verify_token")
async def verify_token(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Verify if the token is valid and return user details.
    """
    return {
        "valid": True,
        "username": current_user.username,
        "role": current_user.role,
        "tenant_id": current_user.tenant_id
    }

@router.post("/extended_token")
async def get_extended_token(request: ExtendedTokenRequest):
    """
    Generate an access token with extended validity period.
    """
    # Find the tenant database
    result = get_tenant_id_and_name_from_slug(request.client_id)
    if not result:
        raise HTTPException(status_code=404, detail="Tenant not found")

    tenant_id = str(result["_id"])
    tenant_database = get_db_from_tenant_id(tenant_id)

    # Authenticate the user
    user = tenant_database.users.find_one({"username": request.username})
    if not user:
        raise HTTPException(status_code=401, detail="User not found")

    if not verify_password(request.password, user["hashed_password"]):
        raise HTTPException(status_code=401, detail="Incorrect credentials")

    # Generate token with extended validity
    access_token = create_access_token(
        data={"sub": user["username"], "user_id": str(user["_id"]), "role": user["role"], "tenant_id": tenant_id},
        expires_delta=timedelta(days=request.days)
    )

    # Get navigation permissions if available
    try:
        nav_permission = tenant_database.settings.find_one({"name": "nav_permission"})
        nav_permission = nav_permission.get(user["role"]) if nav_permission else None
    except Exception:
        nav_permission = None

    # Convert ObjectId to string for JSON serialization
    user = convert_objectid_to_str(user)

    # Log the extended token generation
    loggers.info(f"Extended token generated for user {request.username} with validity of {request.days} days")

    return {
        "id": user["_id"],
        "access_token": access_token,
        "token_type": "bearer",
        "username": user['username'],
        "role": user['role'],
        "tenant_id": tenant_id,
        "tenant_label": result.get("label", result["name"]),
        "tenant_slug": request.client_id,
        "nav_permission": nav_permission,
        "token_validity": {
            "days": request.days,
            "hours": 0,
            "minutes": 0,
            "seconds": 0,
            "total_seconds": timedelta(days=request.days).total_seconds()
        },
        "expires_at": (datetime.now(timezone.utc) + timedelta(days=request.days)).isoformat()
    }

@router.post("/users/invite")
async def invite_user(
    invitation: UserInvitation,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Invite a new agent by generating a registration link with a token.
    Only admins and supervisors can invite agents.
    """
    try:
        tenant_id = current_user.tenant_id
        users_collection = current_user.db.users
        tenant_role = current_user.role

        if tenant_role not in ["admin", "supervisor"]:
            raise HTTPException(status_code=403, detail="You are not authorized to invite agents")

        invited_by = str(current_user.id)

        # Check for existing user
        existing_user = users_collection.find_one({"username": invitation.username})
        if existing_user:
            return {"registration_token": None, "success": False, "msg": "Username already exists"}

        # Check for existing invitation and remove if found
        invitations_collection = current_user.db.invitations
        existing_invitation = invitations_collection.find_one({"username": invitation.username})
        if existing_invitation:
            invitations_collection.delete_many({"username": invitation.username})
            loggers.info(f"Deleted existing invitation for username: {invitation.username}")

        # Create invitation token
        token = create_invitation_token(
            username=invitation.username,
            role=invitation.role,
            invited_by=invited_by,
            tenant_id=tenant_id,
            slug=current_user.tenant_name,  # Using tenant_name as slug
            expires_delta=timedelta(days=invitation.expires_at)
        )

        invitation_record = {
            "username": invitation.username,
            "slug": current_user.tenant_name,
            "token": token,
            "role": invitation.role,
            "invited_by": invited_by,
            "expires_at": datetime.now() + timedelta(days=invitation.expires_at),
            "used": False,
            "permissions": invitation.permissions
        }

        invitations_collection.insert_one(invitation_record)
        return {"registration_token": token, "success": True, "msg": "Token Generated!"}

    except HTTPException as e:
        raise e
    except Exception as e:
        loggers.error(f"Unexpected error in invite_agent: {e}")
        raise HTTPException(status_code=500, detail="Unexpected server error")

@router.post("/users/register")
async def register_user_via_invitation(registration: UserRegistrationViaInvitation):
    """
    Register a new agent using the invitation token.
    """
    try:
        # Verify token
        agent_username, tenant_id, invited_by, role_ = verify_invitation_token(registration.token)

        # Get database and invitation
        tenant_database = get_db_from_tenant_id(tenant_id)
        invitations_collection = tenant_database.invitations
        invitation = invitations_collection.find_one({"token": registration.token})

        if not invitation:
            return {"msg": "Invalid invitation token!", "success": False}
        if invitation.get("used"):
            return {
                "msg": "Invitation token has already been used. Request the supervisor to generate a new one!",
                "success": False,
            }

        users_collection = tenant_database.users

        # Check for existing username
        existing_user = users_collection.find_one({"username": agent_username})
        if existing_user:
            return {"msg": "Username already exists!", "success": False}

        # Hash password and create agent object
        hashed_password = hash_password(registration.password)
        new_agent = {
            "username": registration.username,
            "hashed_password": hashed_password,
            "role": role_,
            "created_by": invited_by,
            "created_at": datetime.now(),
            "permissions": invitation.get("permissions"),
        }

        # Insert agent and mark invitation as used
        result = users_collection.insert_one(new_agent)
        new_agent["_id"] = result.inserted_id

        invitations_collection.update_one(
            {"token": registration.token},
            {"$set": {"used": True}}
        )

        return {"msg": "Agent registered successfully", "success": True}

    except Exception as e:
        loggers.error(f"Unexpected error during agent registration: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@router.post("/user-register")
async def register_user(registration: UserRegistration, current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Register a new user (admin only).
    """
    try:
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Only admins can register users")

        tenant_db = get_db_from_tenant_id(current_user.tenant_id)

        if tenant_db.users.find_one({"username": registration.username}):
            raise HTTPException(status_code=400, detail="Username already exists!")

        user_data = {
            "username": registration.username,
            "hashed_password": hash_password(registration.password),
            "role": registration.role,
            "created_at": datetime.now(),
            **({"type": "bot"} if registration.isBot else {})
        }

        tenant_db.users.insert_one(user_data)
        return {"msg": "User registered successfully", "success": True}

    except PyMongoError as e:
        loggers.error(f"Database error during registration: {e}")
        raise HTTPException(status_code=500, detail="Database operation failed")
    except HTTPException as e:
        raise e
    except Exception as e:
        loggers.error(f"Unexpected error during registration: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")
