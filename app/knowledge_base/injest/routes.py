from fastapi import APIRouter, UploadFile, File, Depends, HTTPException
from typing import List, Optional
from app.knowledge_base.injest.file_processing import (
    process_files,
    extract_text_and_images,
    format_extracted_data,
    create_nodes,
    handle_metadata
)
from app.models.user import UserTenantDB
from app.core.security import require_permission
from app.helper.logger import setup_new_logging

router = APIRouter(prefix="/knowledge-base", tags=["Knowledge Base"])
logger = setup_new_logging(__name__)

@router.post("/upload-pdfs")
async def upload_pdfs_to_index(
    files: List[UploadFile] = File(...),
    current_user: UserTenantDB = Depends(require_permission("documents", "create"))
):
    """
    Upload PDF files and add them to llama index collection
    """
    try:
        # Process files (convert to PDF if needed)
        processed_files = await process_files(files)
        
        # Extract text and images from PDFs
        extracted_data = await extract_text_and_images(processed_files, current_user.minio)
        
        # Format the extracted data
        formatted_data = format_extracted_data(extracted_data, current_user)
        
        # Create llama index nodes
        nodes = create_nodes(formatted_data)
        
        # Handle metadata (add presigned URLs)
        final_nodes = await handle_metadata(nodes, current_user.minio)
        
        # TODO: Add nodes to your llama index collection here
        # index.insert_nodes(final_nodes)
        
        return {
            "message": f"Successfully processed {len(files)} files",
            "nodes_created": len(final_nodes),
            "files_processed": [file.filename for file in files]
        }

    except Exception as e:
        logger.error(f"Error processing files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/process-documents")
async def process_documents(
    files: List[UploadFile] = File(...),
    current_user: UserTenantDB = Depends(require_permission("documents", "create"))
):
    """
    Process documents and add them to the knowledge base.
    This is the main endpoint for document processing with authentication.
    """
    try:
        logger.info(f"User {current_user.username} is processing {len(files)} documents")

        # Process files (convert to PDF if needed)
        processed_files = await process_files(files)

        # Extract text and images from PDFs
        extracted_data = await extract_text_and_images(processed_files, current_user.minio)

        # Format the extracted data
        formatted_data = format_extracted_data(extracted_data, current_user)

        # Create llama index nodes
        nodes = create_nodes(formatted_data)

        # Handle metadata (add presigned URLs)
        final_nodes = await handle_metadata(nodes, current_user.minio)

        # TODO: Add nodes to your llama index collection here
        # index.insert_nodes(final_nodes)

        logger.info(f"Successfully processed {len(files)} files for user {current_user.username}")

        return {
            "message": f"Successfully processed {len(files)} files",
            "nodes_created": len(final_nodes),
            "files_processed": [file.filename for file in files],
            "processed_by": current_user.username,
            "tenant": current_user.tenant_name
        }

    except Exception as e:
        logger.error(f"Error processing documents for user {current_user.username}: {e}")
        raise HTTPException(status_code=500, detail=str(e))