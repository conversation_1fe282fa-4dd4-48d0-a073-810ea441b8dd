# app/core/security.py

from datetime import datetime, timedelta, timezone
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError
from fastapi.security import OAuth2<PERSON>asswordBearer
from fastapi import Depends, HTTPException
import jwt
from typing import Optional
from bson import ObjectId
from app.core.database import get_db_from_tenant_id, get_admin_db
from app.models.user import UserTenantDB, User

# Argon2 password hasher
ph = PasswordHasher()

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

# JWT Configuration - these should be in environment variables
SECRET_KEY = "your-secret-key-change-this-in-production"  # Change this!
ALGORITHM = "HS256"

def hash_password(password: str) -> str:
    """
    Hash a password using Argon2.
    """
    return ph.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against an Argon2 hashed password.
    """
    try:
        ph.verify(hashed_password, plain_password)
        return True
    except VerifyMismatchError:
        return False

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    Create a JWT access token.
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    """
    Get tenant and user information from JWT token.
    """
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        tenant_id: str = payload.get("tenant_id")
        if username is None or tenant_id is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception

    # Get tenant information
    tenant_info = get_admin_db().tenants.find_one({"_id": ObjectId(tenant_id)})
    if not tenant_info:
        raise HTTPException(status_code=404, detail="Tenant not found")

    # Get user from tenant database
    tenant_db = get_db_from_tenant_id(tenant_id)
    user = tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception

    # Check if user is active
    if not user.get("is_active", True):
        raise HTTPException(status_code=403, detail="User account is disabled")

    # Get user permissions
    permissions_setting = tenant_db.settings.find_one({"name": "permissions"})
    user_permissions = permissions_setting.get(user["role"], {}) if permissions_setting else {}

    return UserTenantDB(
        _id=str(user["_id"]),
        username=user["username"],
        role=user["role"],
        tenant_id=tenant_id,
        tenant_name=tenant_info["name"],
        tenant_slug=tenant_info["slug"],
        db=tenant_db,
        adb=get_async_db_from_tenant_id(tenant_id),
        minio=None,  # Will be set up separately
        permissions=user_permissions
    )

def require_user(users: list):
    """
    Dependency to require specific users.
    """
    async def check_username(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_name = user_tenant_info.username
        if user_name not in users:
            raise HTTPException(status_code=403, detail="You are not authorized to carry out this action")
        return user_tenant_info
    return check_username

def min_role(min_role: str):
    """
    Dependency to require minimum role level.
    """
    async def check_role(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_role = user_tenant_info.role
        role_hierarchy = user_tenant_info.db.settings.find_one({"name": "role_hierarchy"})
        if role_hierarchy and role_hierarchy.get('roles'):
            user_level = role_hierarchy['roles'].get(user_role, 0)
            required_level = role_hierarchy['roles'].get(min_role, 999)
            if user_level < required_level:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
        return user_tenant_info
    return check_role

def require_permission(resource: str, action: str):
    """
    Dependency to require specific permission for a resource and action.
    """
    async def check_permission(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        if not user_tenant_info.permissions:
            raise HTTPException(status_code=403, detail="No permissions configured")

        resource_permissions = user_tenant_info.permissions.get(resource, {})
        if not resource_permissions.get(action, False):
            raise HTTPException(
                status_code=403,
                detail=f"Permission denied: {action} on {resource}"
            )
        return user_tenant_info
    return check_permission

def admin_only():
    """
    Dependency to require admin role.
    """
    return min_role("admin")

def supervisor_or_admin():
    """
    Dependency to require supervisor or admin role.
    """
    return min_role("supervisor")

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id: str, slug: str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for user invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id, "slug": slug}
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts user information.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        tenant_id: str = payload.get("tenant_id")
        role: str = payload.get("role")
        invited_by: str = payload.get("invited_by")
        
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or tenant_id is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        return username, tenant_id, invited_by, role

    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")
