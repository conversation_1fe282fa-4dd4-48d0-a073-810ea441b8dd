# app/core/database.py

import os
from pymongo import MongoClient
from pymongo.database import Database
from bson import ObjectId
from typing import Optional

# MongoDB connection string - should be in environment variables
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")

# Global MongoDB client
client = MongoClient(MONGO_URI)

def get_admin_db() -> Database:
    """
    Get the admin database for tenant management.
    """
    return client.admin_db

def get_db_from_tenant_id(tenant_id: str) -> Database:
    """
    Get database connection for a specific tenant.
    """
    admin_db = get_admin_db()
    tenant = admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
    if not tenant:
        raise ValueError(f"Tenant with ID {tenant_id} not found")
    
    database_name = tenant.get("database_name", tenant.get("slug"))
    return client[database_name]

def get_tenant_id_and_name_from_slug(slug: str) -> Optional[dict]:
    """
    Get tenant ID and name from slug.
    """
    admin_db = get_admin_db()
    return admin_db.tenants.find_one({"slug": slug})

def get_async_db_from_tenant_id(tenant_id: str) -> Database:
    """
    Get async database connection for a specific tenant.
    For now, returns the same as sync version.
    """
    return get_db_from_tenant_id(tenant_id)

def init_admin_db():
    """
    Initialize admin database with default tenant if it doesn't exist.
    """
    admin_db = get_admin_db()
    
    # Create default tenant if none exists
    if admin_db.tenants.count_documents({}) == 0:
        default_tenant = {
            "name": "Default Legal Tenant",
            "slug": "legal",
            "label": "Legal",
            "database_name": "legal_db",
            "created_at": "2024-01-01T00:00:00Z"
        }
        result = admin_db.tenants.insert_one(default_tenant)
        print(f"Created default tenant with ID: {result.inserted_id}")
        
        # Initialize the tenant database with default settings
        tenant_db = client[default_tenant["database_name"]]
        
        # Create default settings
        default_settings = [
            {
                "name": "role_hierarchy",
                "roles": {
                    "admin": 3,
                    "supervisor": 2,
                    "agent": 1
                }
            },
            {
                "name": "nav_access",
                "admin": {"all": True},
                "supervisor": {"documents": True, "users": True},
                "agent": {"documents": True}
            },
            {
                "name": "token_validity",
                "days": 0,
                "hours": 6,
                "minutes": 0,
                "seconds": 0
            }
        ]
        
        for setting in default_settings:
            tenant_db.settings.insert_one(setting)
        
        print(f"Initialized tenant database: {default_tenant['database_name']}")

def create_default_admin_user(tenant_slug: str = "legal", username: str = "admin", password: str = "admin123"):
    """
    Create a default admin user for testing purposes.
    """
    from app.core.security import hash_password
    
    tenant_info = get_tenant_id_and_name_from_slug(tenant_slug)
    if not tenant_info:
        raise ValueError(f"Tenant with slug {tenant_slug} not found")
    
    tenant_db = get_db_from_tenant_id(str(tenant_info["_id"]))
    
    # Check if admin user already exists
    existing_user = tenant_db.users.find_one({"username": username})
    if existing_user:
        print(f"Admin user '{username}' already exists")
        return
    
    # Create admin user
    admin_user = {
        "username": username,
        "hashed_password": hash_password(password),
        "role": "admin",
        "created_at": "2024-01-01T00:00:00Z"
    }
    
    result = tenant_db.users.insert_one(admin_user)
    print(f"Created admin user '{username}' with ID: {result.inserted_id}")
