# app/core/database.py

import os
from pymongo import MongoClient, AsyncMongoClient
from pymongo.database import Database
from bson import ObjectId
from typing import Optional
from datetime import datetime
from fastapi import HTTPException

# MongoDB connection string - should be in environment variables
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")
ADMIN_DB_NAME = os.getenv("ADMIN_DB_NAME", "legal_tenant_admin")

# Global MongoDB client
client = MongoClient(MONGO_URI)
async_client = AsyncMongoClient(MONGO_URI)

def get_admin_db() -> Database:
    """
    Get the tenant admin database for tenant management.
    This database stores all tenant configurations and metadata.
    """
    return client[ADMIN_DB_NAME]

def get_async_admin_db():
    """
    Get async tenant admin database.
    """
    return async_client[ADMIN_DB_NAME]

def get_db_from_tenant_id(tenant_id: str) -> Database:
    """
    Get database connection for a specific tenant.
    """
    admin_db = get_admin_db()
    tenant = admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
    if not tenant:
        raise HTTPException(status_code=404, detail=f"Tenant with ID {tenant_id} not found")

    database_name = tenant.get("database_name")
    if not database_name:
        raise HTTPException(status_code=500, detail=f"Database name not found for tenant {tenant_id}")

    return client[database_name]

def get_async_db_from_tenant_id(tenant_id: str):
    """
    Get async database connection for a specific tenant.
    """
    admin_db = get_admin_db()
    tenant = admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
    if not tenant:
        raise HTTPException(status_code=404, detail=f"Tenant with ID {tenant_id} not found")

    database_name = tenant.get("database_name")
    if not database_name:
        raise HTTPException(status_code=500, detail=f"Database name not found for tenant {tenant_id}")

    return async_client[database_name]

def get_tenant_id_and_name_from_slug(slug: str) -> Optional[dict]:
    """
    Get tenant ID and name from slug.
    """
    admin_db = get_admin_db()
    return admin_db.tenants.find_one({"slug": slug})

def init_tenant_admin_db():
    """
    Initialize the tenant admin database with required collections and default data.
    """
    admin_db = get_admin_db()

    # Create indexes for better performance
    admin_db.tenants.create_index("slug", unique=True)
    admin_db.tenants.create_index("database_name", unique=True)

    # Create default super admin user in admin database if it doesn't exist
    super_admin = admin_db.users.find_one({"username": "superadmin"})
    if not super_admin:
        from app.core.security import hash_password
        super_admin_data = {
            "username": "superadmin",
            "hashed_password": hash_password("superadmin123"),
            "role": "superadmin",
            "created_at": datetime.now(),
            "is_active": True,
            "permissions": {"all": True}
        }
        result = admin_db.users.insert_one(super_admin_data)
        print(f"Created super admin user with ID: {result.inserted_id}")

    # Create default tenant if none exists
    if admin_db.tenants.count_documents({}) == 0:
        create_default_tenant()

    print("Tenant admin database initialized successfully")

def create_default_tenant():
    """
    Create a default legal tenant.
    """
    admin_db = get_admin_db()

    default_tenant = {
        "name": "Legal Document Processing",
        "slug": "legal",
        "label": "Legal",
        "database_name": "legal_tenant_db",
        "created_at": datetime.now(),
        "is_active": True,
        "settings": {
            "max_users": 100,
            "max_storage_gb": 50,
            "features": ["document_processing", "user_management", "analytics"]
        }
    }

    result = admin_db.tenants.insert_one(default_tenant)
    print(f"Created default tenant with ID: {result.inserted_id}")

    # Initialize the tenant database
    init_tenant_database(str(result.inserted_id))

    return result.inserted_id

def init_tenant_database(tenant_id: str):
    """
    Initialize a tenant's database with required collections and default settings.
    """
    tenant_db = get_db_from_tenant_id(tenant_id)

    # Create required collections
    collections = ["users", "settings", "documents", "invitations", "activity_logs"]
    for collection_name in collections:
        if collection_name not in tenant_db.list_collection_names():
            tenant_db.create_collection(collection_name)

    # Create indexes
    tenant_db.users.create_index("username", unique=True)
    tenant_db.documents.create_index("created_at")
    tenant_db.activity_logs.create_index("timestamp")

    # Insert default settings
    default_settings = [
        {
            "name": "role_hierarchy",
            "roles": {
                "admin": 3,
                "supervisor": 2,
                "user": 1
            }
        },
        {
            "name": "permissions",
            "admin": {
                "documents": {"create": True, "read": True, "update": True, "delete": True},
                "users": {"create": True, "read": True, "update": True, "delete": True},
                "settings": {"read": True, "update": True}
            },
            "supervisor": {
                "documents": {"create": True, "read": True, "update": True, "delete": False},
                "users": {"create": True, "read": True, "update": True, "delete": False},
                "settings": {"read": True, "update": False}
            },
            "user": {
                "documents": {"create": True, "read": True, "update": False, "delete": False},
                "users": {"create": False, "read": False, "update": False, "delete": False},
                "settings": {"read": False, "update": False}
            }
        },
        {
            "name": "token_validity",
            "days": 0,
            "hours": 8,
            "minutes": 0,
            "seconds": 0
        }
    ]

    for setting in default_settings:
        existing = tenant_db.settings.find_one({"name": setting["name"]})
        if not existing:
            tenant_db.settings.insert_one(setting)

    print(f"Initialized tenant database: {tenant_db.name}")

def create_default_tenant_admin(tenant_id: str, username: str = "admin", password: str = "admin123"):
    """
    Create a default admin user for a tenant.
    """
    from app.core.security import hash_password

    tenant_db = get_db_from_tenant_id(tenant_id)

    # Check if admin user already exists
    existing_user = tenant_db.users.find_one({"username": username})
    if existing_user:
        print(f"Admin user '{username}' already exists in tenant {tenant_id}")
        return existing_user["_id"]

    # Create admin user
    admin_user = {
        "username": username,
        "hashed_password": hash_password(password),
        "role": "admin",
        "created_at": datetime.now(),
        "created_by": "system",
        "is_active": True
    }

    result = tenant_db.users.insert_one(admin_user)
    print(f"Created admin user '{username}' with ID: {result.inserted_id}")
    return result.inserted_id
