# app/shared/security/dependencies.py

from fastapi import Depends, HTTPException
from fastapi.security import <PERSON>Auth2<PERSON><PERSON>wordBearer
from bson import ObjectId

from app.shared.database.connection import get_admin_db, get_db_from_tenant_id, get_async_db_from_tenant_id
from app.shared.database.models import UserTenantDB, SuperAdmin
from .auth import decode_access_token

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/v1/auth/login")

async def get_current_user(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    """
    Get current user and tenant information from JWT token.
    """
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = decode_access_token(token)
        username: str = payload.get("sub")
        tenant_id: str = payload.get("tenant_id")
        if username is None or tenant_id is None:
            raise credentials_exception
    except ValueError:
        raise credentials_exception

    # Get tenant information
    tenant_info = get_admin_db().tenants.find_one({"_id": ObjectId(tenant_id)})
    if not tenant_info:
        raise HTTPException(status_code=404, detail="Tenant not found")
    
    # Get user from tenant database
    tenant_db = get_db_from_tenant_id(tenant_id)
    user = tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception
    
    # Check if user is active
    if not user.get("is_active", True):
        raise HTTPException(status_code=403, detail="User account is disabled")
    
    # Get user permissions - first check user document, then fallback to settings
    user_permissions = user.get("permissions", {})
    if not user_permissions:
        # Fallback to role-based permissions from settings
        permissions_setting = tenant_db.settings.find_one({"name": "permissions"})
        user_permissions = permissions_setting.get(user["role"], {}) if permissions_setting else {}
    
    return UserTenantDB(
        _id=str(user["_id"]),
        username=user["username"],
        role=user["role"],
        tenant_id=tenant_id,
        tenant_name=tenant_info["name"],
        tenant_slug=tenant_info["slug"],
        db=tenant_db,
        adb=get_async_db_from_tenant_id(tenant_id),
        minio=None,  # Will be set up separately
        permissions=user_permissions
    )

def get_super_admin() -> SuperAdmin:
    """
    Dependency to verify super admin access.
    For now, this is a placeholder - in production you'd verify JWT tokens.
    """
    admin_db = get_admin_db()
    super_admin = admin_db.users.find_one({"role": "superadmin", "is_active": True})
    if not super_admin:
        raise HTTPException(status_code=403, detail="Super admin access required")
    return SuperAdmin(**super_admin)
