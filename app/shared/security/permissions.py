# app/shared/security/permissions.py

from fastapi import Depends, HTTPException
from app.shared.database.models import UserTenantDB
from .dependencies import get_current_user

def min_role(min_role: str):
    """
    Dependency to require minimum role level.
    """
    async def check_role(user_tenant_info: UserTenantDB = Depends(get_current_user)):
        user_role = user_tenant_info.role
        role_hierarchy = user_tenant_info.db.settings.find_one({"name": "role_hierarchy"})
        if role_hierarchy and role_hierarchy.get('roles'):
            user_level = role_hierarchy['roles'].get(user_role, 0)
            required_level = role_hierarchy['roles'].get(min_role, 999)
            if user_level < required_level:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
        return user_tenant_info
    return check_role

def require_permission(resource: str, action: str):
    """
    Dependency to require specific permission for a resource and action.
    """
    async def check_permission(user_tenant_info: UserTenantDB = Depends(get_current_user)):
        if not user_tenant_info.permissions:
            raise HTTPException(status_code=403, detail="No permissions configured")
        
        resource_permissions = user_tenant_info.permissions.get(resource, {})
        if not resource_permissions.get(action, False):
            raise HTTPException(
                status_code=403, 
                detail=f"Permission denied: {action} on {resource}"
            )
        return user_tenant_info
    return check_permission

def admin_only():
    """
    Dependency to require admin role.
    """
    return min_role("admin")

def supervisor_or_admin():
    """
    Dependency to require supervisor or admin role.
    """
    return min_role("supervisor")

def require_user(users: list):
    """
    Dependency to require specific users.
    """
    async def check_username(user_tenant_info: UserTenantDB = Depends(get_current_user)):
        user_name = user_tenant_info.username
        if user_name not in users:
            raise HTTPException(status_code=403, detail="You are not authorized to carry out this action")
        return user_tenant_info
    return check_username
