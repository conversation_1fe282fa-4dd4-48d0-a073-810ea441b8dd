# app/shared/database/__init__.py
"""
Database module for MongoDB connections and utilities.
"""

from .connection import (
    get_admin_db,
    get_async_admin_db,
    get_db_from_tenant_id,
    get_async_db_from_tenant_id,
    get_tenant_id_and_name_from_slug,
    init_tenant_admin_db,
    create_default_tenant,
    init_tenant_database,
    create_default_tenant_admin
)

from .models import User, UserTenantDB, SuperAdmin, TenantInfo

__all__ = [
    "get_admin_db",
    "get_async_admin_db", 
    "get_db_from_tenant_id",
    "get_async_db_from_tenant_id",
    "get_tenant_id_and_name_from_slug",
    "init_tenant_admin_db",
    "create_default_tenant",
    "init_tenant_database",
    "create_default_tenant_admin",
    "User",
    "UserTenantDB",
    "SuperAdmin",
    "TenantInfo"
]
