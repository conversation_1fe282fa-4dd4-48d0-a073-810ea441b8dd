# app/shared/database/models.py

from pydantic import BaseModel, Field, field_validator
from typing import Any, Dict, Literal, Optional
from pymongo.database import Database
from pymongo.asynchronous.database import AsyncDatabase
from minio import Minio
from datetime import datetime
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "user"]
    is_active: bool = True
    created_at: Optional[datetime] = None
    created_by: Optional[str] = None

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
    
class UserTenantDB(BaseModel):
    """
    User document from the tenant database -> users collection with tenant context
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "user"]
    tenant_id: str
    tenant_name: str
    tenant_slug: str
    db: Database
    adb: AsyncDatabase
    minio: Optional[Minio] = None
    permissions: Optional[Dict] = None
    qdrant: Optional[Any] = None  # Lazy-loaded Qdrant client

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

    async def init_qdrant(self):
        """
        Initialize Qdrant client only when needed to save resources.
        """
        if self.qdrant is None:
            from qdrant_client import AsyncQdrantClient

            # Get Qdrant config from tenant environment
            qdrant_config = await self.adb.settings.find_one({"name": "qdrant_config"})

            if not qdrant_config:
                # Set default Qdrant config with collection names
                default_config = {
                    "name": "qdrant_config",
                    "host": "*************",
                    "port": 6333,
                    "timeout": 60,
                    "page_collection": f"{self.tenant_slug}_page",
                    "legal_sentence": f"{self.tenant_slug}_sentence",
                    "whole_docs_collection": f"{self.tenant_slug}_whole_documents",
                    "split_docs_collection": f"{self.tenant_slug}_split_documents"
                }
                await self.adb.settings.insert_one(default_config)
                qdrant_config = default_config

            # Also ensure MinIO config exists
            await self._ensure_minio_config()

            self.qdrant = AsyncQdrantClient(
                host=qdrant_config["host"],
                port=qdrant_config["port"],
                timeout=qdrant_config.get("timeout", 60)
            )

        return self.qdrant

    async def _ensure_minio_config(self):
        """
        Ensure MinIO configuration exists in tenant settings.
        """
        try:
            minio_config = await self.adb.settings.find_one({"name": "minio_config"})

            if not minio_config:
                # Set default MinIO config
                default_minio_config = {
                    "name": "minio_config",
                    "access_key": "minio-admin",
                    "secret_key": "aLongPassword123",
                    "bucket_name": f"{self.tenant_slug}_minio",
                    "minio_url": "minio.nextai.asia",
                    "secure": True
                }
                await self.adb.settings.insert_one(default_minio_config)
                logger.info(f"Created default MinIO config for tenant {self.tenant_slug}")

        except Exception as e:
            logger.error(f"Error ensuring MinIO config: {e}")

    async def get_minio_config(self) -> dict:
        """
        Get MinIO configuration from tenant settings.
        """
        try:
            minio_config = await self.adb.settings.find_one({"name": "minio_config"})

            if not minio_config:
                await self._ensure_minio_config()
                minio_config = await self.adb.settings.find_one({"name": "minio_config"})

            return {
                "access_key": minio_config.get("access_key", "minio-admin"),
                "secret_key": minio_config.get("secret_key", "aLongPassword123"),
                "bucket_name": minio_config.get("bucket_name", f"{self.tenant_slug}_minio"),
                "minio_url": minio_config.get("minio_url", "minio.nextai.asia"),
                "secure": minio_config.get("secure", True)
            }

        except Exception as e:
            logger.error(f"Error getting MinIO config: {e}")
            # Return fallback config
            return {
                "access_key": "minio-admin",
                "secret_key": "aLongPassword123",
                "bucket_name": f"{self.tenant_slug}_minio",
                "minio_url": "minio.nextai.asia",
                "secure": True
            }

    class Config:
        arbitrary_types_allowed = True

class SuperAdmin(BaseModel):
    """
    Super admin user from the tenant admin database
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["superadmin"]
    is_active: bool = True
    created_at: Optional[datetime] = None
    permissions: Dict = {"all": True}

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

class TenantInfo(BaseModel):
    """
    Tenant information from the admin database
    """
    id: Any = Field(alias="_id")
    name: str
    slug: str
    label: str
    database_name: str
    is_active: bool = True
    created_at: Optional[datetime] = None
    settings: Optional[Dict] = None

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
