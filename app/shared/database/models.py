# app/shared/database/models.py

from pydantic import BaseModel, Field, field_validator
from typing import Any, Dict, Literal, Optional
from pymongo.database import Database
from minio import Minio
from datetime import datetime

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "user"]
    is_active: bool = True
    created_at: Optional[datetime] = None
    created_by: Optional[str] = None

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
    
class UserTenantDB(BaseModel):
    """
    User document from the tenant database -> users collection with tenant context
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "user"]
    tenant_id: str
    tenant_name: str
    tenant_slug: str
    db: Database
    adb: Database
    minio: Optional[Minio] = None
    permissions: Optional[Dict] = None

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

    class Config:
        arbitrary_types_allowed = True

class SuperAdmin(BaseModel):
    """
    Super admin user from the tenant admin database
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["superadmin"]
    is_active: bool = True
    created_at: Optional[datetime] = None
    permissions: Dict = {"all": True}

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

class TenantInfo(BaseModel):
    """
    Tenant information from the admin database
    """
    id: Any = Field(alias="_id")
    name: str
    slug: str
    label: str
    database_name: str
    is_active: bool = True
    created_at: Optional[datetime] = None
    settings: Optional[Dict] = None

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
