# app/shared/database/models.py

from pydantic import BaseModel, Field, field_validator
from typing import Any, Dict, Literal, Optional
from pymongo.database import Database
from minio import Minio
from datetime import datetime

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "user"]
    is_active: bool = True
    created_at: Optional[datetime] = None
    created_by: Optional[str] = None

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
    
class UserTenantDB(BaseModel):
    """
    User document from the tenant database -> users collection with tenant context
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "user"]
    tenant_id: str
    tenant_name: str
    tenant_slug: str
    db: Database
    adb: Database
    minio: Optional[Minio] = None
    permissions: Optional[Dict] = None
    qdrant: Optional[Any] = None  # Lazy-loaded Qdrant client

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

    async def init_qdrant(self):
        """
        Initialize Qdrant client only when needed to save resources.
        """
        if self.qdrant is None:
            from qdrant_client import AsyncQdrantClient

            # Get Qdrant config from tenant environment
            qdrant_config = await self.adb.settings.find_one({"name": "qdrant_config"})

            if not qdrant_config:
                # Set default Qdrant config
                default_config = {
                    "name": "qdrant_config",
                    "host": "*************",
                    "port": 6333,
                    "timeout": 60
                }
                await self.adb.settings.insert_one(default_config)
                qdrant_config = default_config

            self.qdrant = AsyncQdrantClient(
                host=qdrant_config["host"],
                port=qdrant_config["port"],
                timeout=qdrant_config.get("timeout", 60)
            )

        return self.qdrant

    class Config:
        arbitrary_types_allowed = True

class SuperAdmin(BaseModel):
    """
    Super admin user from the tenant admin database
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["superadmin"]
    is_active: bool = True
    created_at: Optional[datetime] = None
    permissions: Dict = {"all": True}

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

class TenantInfo(BaseModel):
    """
    Tenant information from the admin database
    """
    id: Any = Field(alias="_id")
    name: str
    slug: str
    label: str
    database_name: str
    is_active: bool = True
    created_at: Optional[datetime] = None
    settings: Optional[Dict] = None

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)
