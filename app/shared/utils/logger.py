# app/shared/utils/logger.py

import logging
import sys
from typing import Optional

def setup_new_logging(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    Set up a new logger with the given name and level.
    
    Args:
        name: The name of the logger (usually __name__)
        level: The logging level (default: INFO)
    
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Avoid adding multiple handlers to the same logger
    if logger.handlers:
        return logger
    
    logger.setLevel(level)
    
    # Create console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(level)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(handler)
    
    return logger
