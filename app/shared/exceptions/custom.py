# app/shared/exceptions/custom.py

class AuthenticationError(Exception):
    """Raised when authentication fails."""
    pass

class AuthorizationError(Exception):
    """Raised when user lacks required permissions."""
    pass

class TenantNotFoundError(Exception):
    """Raised when tenant is not found."""
    pass

class UserNotFoundError(Exception):
    """Raised when user is not found."""
    pass

class ValidationError(Exception):
    """Raised when data validation fails."""
    pass
