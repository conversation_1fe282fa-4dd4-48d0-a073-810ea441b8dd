from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from contextlib import asynccontextmanager

from app.auth.routes import router as auth_router
from app.knowledge_base.injest.routes import router as knowledge_router
from app.core.database import init_admin_db, create_default_admin_user
from app.helper.logger import setup_new_logging

# Initialize logger
logger = setup_new_logging(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for the FastAPI application.
    """
    # Startup: Initialize database
    logger.info("Initializing database and creating default tenant...")
    init_admin_db()

    # Create default admin user (only for development)
    try:
        create_default_admin_user()
    except Exception as e:
        logger.warning(f"Could not create default admin user: {e}")

    logger.info("Application startup completed")

    # Yield control back to FastAPI
    yield

    # Shutdown
    logger.info("Application shutting down")

app = FastAPI(
    title="Legal Document Processing API",
    description="Legal document processing API with Argon2 authentication",
    version="0.1.0",
    lifespan=lifespan
)

origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router, prefix="/auth")
app.include_router(knowledge_router)

@app.get("/")
async def root():
    return RedirectResponse(url="/docs")

@app.get("/health")
async def health_check():
    """Health check endpoint for load balancer and monitoring."""
    return {
        "status": "healthy",
        "service": "legal-api",
        "version": "0.1.0"
    }